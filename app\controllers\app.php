<?php

// B<PERSON>t báo lỗi toàn bộ để dễ phát hiện lỗi trong quá trình phát triển
error_reporting(E_ALL);
// Đặt múi giờ mặc định cho hệ thống là 'Asia/Ho_Chi_Minh'
date_default_timezone_set('Asia/Ho_Chi_Minh');

// Nạp file RSACrypt.php chứa định nghĩa lớp RSACrypt để xử lý RSA
require_once __DIR__ . '/libs/RSACrypt.php';
// Khởi tạo đối tượng RSACrypt để sử dụng cho các hàm mã hóa RSA
$rsa = new RSACrypt();

/**===================*
 * Các hàm mã hóa RSA *
 *====================**/

/**
 * Hàm encryptData: Mã hóa dữ liệu sử dụng khóa RSA.
 *
 * @param mixed $data Dữ liệu cần mã hóa.
 * @return string Dữ liệu đã mã hóa.
 */
function encryptData($data)
{
    global $rsa; // Sử dụng đối tượng RSACrypt toàn cục
    // Đặt khóa riêng của client (dùng để ký số hoặc xác thực nguồn gốc)
    $rsa->setPrivateKey(__DIR__ . '/libs/clientPrivate.pem');
    // Đặt khóa công khai của server (dùng để mã hóa dữ liệu gửi đến server)
    $rsa->setPublicKey(__DIR__ . '/libs/serverPublic.pem');
    // Mã hóa dữ liệu sử dụng khóa công khai của server
    return $rsa->encryptWithPublicKey($data);
}

/**
 * Hàm decodecryptData: Giải mã dữ liệu mã hóa RSA.
 *
 * @param string $data Dữ liệu đã mã hóa.
 * @return string Dữ liệu đã giải mã.
 */
function decodecryptData($data)
{
    global $rsa; // Sử dụng đối tượng RSACrypt toàn cục
    // Đặt khóa riêng của server (để giải mã dữ liệu)
    $rsa->setPrivateKey(__DIR__ . '/libs/serverPrivate.pem');
    // Đặt khóa công khai của client (để xác minh chữ ký nếu cần)
    $rsa->setPublicKey(__DIR__ . '/libs/clientPublic.pem');
    // Giải mã dữ liệu sử dụng khóa riêng của server
    return $rsa->decryptWithPrivateKey($data);
}

/**==================================*
 * Các hàm mã hóa đối xứng với AES-256 *
 *===================================**/

/**
 * Hàm encrypt: Mã hóa dữ liệu sử dụng thuật toán AES-256-CBC.
 *
 * @param string $data Dữ liệu cần mã hóa.
 * @param string $key Khóa mã hóa.
 * @return string Dữ liệu đã mã hóa dưới dạng chuỗi Base64.
 */
function encrypt($data, $key)
{
    $cipher = 'AES-256-CBC'; // Thuật toán mã hóa
    // Lấy kích thước IV cần thiết cho thuật toán AES-256-CBC
    $ivSize = openssl_cipher_iv_length($cipher);
    // Tạo IV ngẫu nhiên với độ dài vừa xác định
    $iv = openssl_random_pseudo_bytes($ivSize);
    // Mã hóa dữ liệu sử dụng thuật toán AES-256-CBC với khóa, chế độ RAW_DATA để nhận kết quả nhị phân
    $encryptedData = openssl_encrypt($data, $cipher, $key, OPENSSL_RAW_DATA, $iv);
    // Nối IV với dữ liệu mã hóa rồi chuyển thành chuỗi Base64 để dễ lưu trữ
    return base64_encode($iv . $encryptedData);
}

/**
 * Hàm decrypt: Giải mã dữ liệu đã mã hóa bằng AES-256-CBC.
 *
 * @param string $encryptedData Dữ liệu đã mã hóa dạng Base64.
 * @param string $key Khóa giải mã.
 * @return string Dữ liệu đã giải mã.
 */
function decrypt($encryptedData, $key)
{
    $cipher = 'AES-256-CBC'; // Thuật toán giải mã
    // Giải mã chuỗi Base64 để lấy lại dữ liệu nhị phân (IV + dữ liệu mã hóa)
    $encryptedData = base64_decode($encryptedData);
    // Lấy kích thước IV cho thuật toán
    $ivSize = openssl_cipher_iv_length($cipher);
    // Tách phần IV (phần đầu chuỗi)
    $iv = substr($encryptedData, 0, $ivSize);
    // Tách phần dữ liệu mã hóa sau IV
    $data = substr($encryptedData, $ivSize);
    // Giải mã dữ liệu sử dụng thuật toán, khóa và IV đã lấy ra
    return openssl_decrypt($data, $cipher, $key, OPENSSL_RAW_DATA, $iv);
}

/**=======================*
 * Cấu hình Session/Cookie *
 *========================**/

// Xác định thời gian hết hạn cho cookie: 30 ngày từ thời điểm hiện tại
$expiration = time() + (30 * 24 * 60 * 60);

// Thiết lập các tham số cho cookie của session:
// - httponly: Ngăn JavaScript truy cập cookie
// - expires: Thời gian hết hạn của cookie
session_set_cookie_params([
    'lifetime' => $expiration,
    'httponly' => true,
]);
// Đặt tên cho session là 'etoken'
session_name('etoken');

// Nếu cookie 'duogxaolin' chưa tồn tại và cookie 'etoken' đã có, tạo cookie 'duogxaolin'
if (empty($_COOKIE['duogxaolin']) && !empty($_COOKIE['etoken'])) {
    session_start();              // Khởi tạo session
    $sessionId = session_id();      // Lấy ID của session hiện tại
    // Mã hóa sessionId sau khi hash (dùng password_hash và md5 kép)
    $encryptedSessionId = encrypt(password_hash(md5(md5($sessionId)), PASSWORD_DEFAULT), $_COOKIE['etoken']);
    // Đặt cookie 'duogxaolin' với giá trị đã mã hóa, thời gian hết hạn và đường dẫn '/'
    setcookie('duogxaolin', $encryptedSessionId, $expiration, '/');
}

// Nếu cookie 'python_session' chưa tồn tại, tạo cookie với giá trị là thời gian hiện tại
if (empty($_COOKIE['python_session'])) {
    setcookie("python_session", time(), $expiration, "/");
}

// Nếu cookie 'golang_session' chưa tồn tại, tạo cookie với giá trị là hash của thời gian hiện tại
if (empty($_COOKIE['golang_session'])) {
    setcookie("golang_session", password_hash(md5(md5(time())), PASSWORD_DEFAULT), $expiration, "/");
}

// Đặt header HTTP để đánh lạc hướng (che dấu thông tin thật của server)
header('X-Powered-By: PYTHON/3.9.7,GOLANG/1.17.1');
header('Server: PYTHON/3.9.7,GOLANG/1.17.1');

// Nếu session chưa được khởi tạo, bắt đầu session
if (session_status() !== PHP_SESSION_ACTIVE) {
    session_start();
}

// Lấy tên domain từ biến HTTP_HOST và loại bỏ tiền tố 'www.' nếu có
$domain = preg_replace('/^www\./', '', $_SERVER['HTTP_HOST']);
require_once($_SERVER['DOCUMENT_ROOT'] . '/app/controllers/system_core.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/app/controllers/core/logic.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/app/controllers/core/api.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/app/controllers/core/msg.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/app/controllers/core/user.php');

require_once($_SERVER['DOCUMENT_ROOT'] . '/app/controllers/core/google/login.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/app/controllers/core/zalo/login.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/app/controllers/core/discord/login.php');