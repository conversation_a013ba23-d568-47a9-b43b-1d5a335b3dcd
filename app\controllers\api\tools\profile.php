<?php
header("Content-Type: image/svg+xml");

$name = isset($_GET['name']) ? trim($_GET['name']) : 'User';
$words = explode(' ', $name);
$initials = '';

if (count($words) >= 2) {
    $initials = mb_strtoupper(mb_substr($words[0], 0, 1)) . mb_strtoupper(mb_substr(end($words), 0, 1));
} else {
    $initials = mb_strtoupper(mb_substr($name, 0, 2));
}

// Tạo màu từ hash
$hash = md5($name);
$r = hexdec(substr($hash, 0, 2));
$g = hexdec(substr($hash, 2, 2));
$b = hexdec(substr($hash, 4, 2));

$min_brightness = 100;
$r = max($r, $min_brightness);
$g = max($g, $min_brightness);
$b = max($b, $min_brightness);

$color = sprintf("#%02x%02x%02x", $r, $g, $b);

// Render SVG
echo <<<SVG
<svg width="100" height="100" xmlns="http://www.w3.org/2000/svg">
  <rect width="100" height="100" fill="$color" rx="10"/>
  <text x="50%" y="55%" font-size="40" fill="#fff" text-anchor="middle" alignment-baseline="middle" font-family="sans-serif">$initials</text>
</svg>
SVG;
