<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/config.php');
require_once($_SERVER['DOCUMENT_ROOT'] . '/app/controllers/client/main.php');
$action = $_POST['action'] ?? null;
$users = $connects->requireLogin();
if (!$users) {
    setcookie('session_token', '', time() - 3600, '/');
    session_destroy();
    die(json_encode([
        'status' => 'error',
        'msg' => 'Vui lòng đăng nhập !'
    ]));
}
$check_user = $duogxaolin->getRow("SELECT * FROM `user` WHERE `id` = ? ",[$users['id']]);
if (!$check_user) {
    setcookie('session_token', '', time() - 3600, '/');
    
    session_destroy();
    die(json_encode([
        'status' => 'error',
        'msg' => '<PERSON><PERSON><PERSON> khoản không tồn tại !'
    ]));
}
if($action == 'all'){
    $duogxaolin->update("user_devices", [
        'status'  => 0
      ], " `user_id` = ? ",[$check_user['id']]);
      die(json_encode([
        'status' => 'success',
        'msg' => 'Đăng xuất tất cả thiết bị thành công !'
    ]));
}else if($action == 'device'){
    $device = xss($_POST['device'] ?? '');
    $id = xss($_POST['id'] ?? '');
    if(empty($id)) {
        die(json_encode([
            'status' => 'error',
            'msg' => 'Vui lòng chọn thiết bị !'
        ]));
    }
    if(empty($device)) {
        die(json_encode([
            'status' => 'error',
            'msg' => 'Vui lòng chọn thiết bị !'
        ]));
    }
    $check_device = $duogxaolin->getRow("SELECT * FROM `user_devices` 
    WHERE `id` = ? AND `device_id` = ? AND `user_id` = ? AND `status` = ? "
    ,[$id,$device,$check_user['id'],1]
    );
    if (!$check_device) {
        die(json_encode([
            'status' => 'error',
            'msg' => 'Thiết bị không chính xác!'
        ]));
    }
    $duogxaolin->update("user_devices", [
        'status'  => 0
      ], " `id` = ? ",[$id]);
        die(json_encode([
            'status' => 'success',
            'msg' => 'Đăng xuất thiết bị thành công !'
        ]));
}else{
    $auth->logout('json');
}

?>