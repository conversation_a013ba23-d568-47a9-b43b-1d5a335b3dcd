# Social Login Setup Guide

This guide explains how to set up Zalo and Discord social login integration for your gaming website.

## Overview

The social login system has been implemented with the following components:

### Backend Files Created:
- `app/controllers/core/zalo/login.php` - Zalo OAuth configuration
- `app/controllers/core/discord/login.php` - Discord OAuth configuration  
- `app/controllers/api/zalo/login.php` - Zalo authentication handler
- `app/controllers/api/discord/login.php` - Discord authentication handler
- `database/migrations/add_social_login_columns.sql` - Database schema updates
- `database/migrate.php` - Migration runner script

### Frontend Integration:
- Social login buttons are already implemented in `public/views/customer/login.php`
- Responsive design with desktop and mobile layouts
- Gaming-themed styling with brand colors

## Setup Instructions

### 1. Database Migration

First, run the database migration to add the required columns:

```bash
php database/migrate.php
```

This will add `zalo_id` and `discord_id` columns to your `user` table.

### 2. Zalo OAuth Setup

#### 2.1 Create Zalo App
1. Go to [Zalo Developers](https://developers.zalo.me/)
2. Create a new app or use existing one
3. Get your App ID and App Secret

#### 2.2 Configure Redirect URI
Add this redirect URI in your Zalo app settings:
```
https://yourdomain.com/zalo/api/login
```

#### 2.3 Update Configuration
Edit `app/controllers/core/zalo/login.php`:
```php
$app_id = 'YOUR_ACTUAL_ZALO_APP_ID';
$app_secret = 'YOUR_ACTUAL_ZALO_APP_SECRET';
```

### 3. Discord OAuth Setup

#### 3.1 Create Discord App
1. Go to [Discord Developer Portal](https://discord.com/developers/applications)
2. Create a new application
3. Go to OAuth2 section
4. Get your Client ID and Client Secret

#### 3.2 Configure Redirect URI
Add this redirect URI in your Discord app OAuth2 settings:
```
https://yourdomain.com/discord/api/login
```

#### 3.3 Update Configuration
Edit `app/controllers/core/discord/login.php`:
```php
$client_id = 'YOUR_ACTUAL_DISCORD_CLIENT_ID';
$client_secret = 'YOUR_ACTUAL_DISCORD_CLIENT_SECRET';
```

### 4. URL Routing Setup

Ensure your web server routes these URLs correctly:

- `/zalo/api/login` → `app/controllers/api/zalo/login.php`
- `/discord/api/login` → `app/controllers/api/discord/login.php`

#### Apache (.htaccess example):
```apache
RewriteRule ^zalo/api/login$ app/controllers/api/zalo/login.php [L]
RewriteRule ^discord/api/login$ app/controllers/api/discord/login.php [L]
```

#### Nginx example:
```nginx
location /zalo/api/login {
    try_files $uri /app/controllers/api/zalo/login.php;
}

location /discord/api/login {
    try_files $uri /app/controllers/api/discord/login.php;
}
```

## How It Works

### Authentication Flow

1. **User clicks social login button** → Redirected to OAuth provider
2. **User authorizes app** → Provider redirects back with authorization code
3. **Backend exchanges code for access token** → Gets user information
4. **System checks if user exists**:
   - **Existing user**: Logs them in
   - **New user**: Creates account and logs them in
5. **Session established** → User redirected to dashboard

### User Account Handling

#### For Existing Users:
- Matched by social ID or email
- Social ID added to existing account if missing
- Normal login flow continues

#### For New Users:
- Username generated from social platform data
- Email from social platform or generated placeholder
- Account auto-verified (no email confirmation needed)
- Welcome email sent
- Automatic login after registration

### Security Features

- **CSRF Protection**: State parameter in OAuth flow
- **Secure Cookies**: HttpOnly and Secure flags
- **Device Tracking**: Full device fingerprinting
- **Session Management**: Proper token-based sessions
- **Error Handling**: Graceful error handling with redirects

## Testing

### Test Zalo Login:
1. Visit your login page
2. Click the Zalo login button
3. Authorize the app in Zalo
4. Should be redirected back and logged in

### Test Discord Login:
1. Visit your login page  
2. Click the Discord login button
3. Authorize the app in Discord
4. Should be redirected back and logged in

## Troubleshooting

### Common Issues:

1. **"Invalid redirect URI"**
   - Check OAuth app settings match your domain
   - Ensure HTTPS is used in production

2. **"App ID/Client ID not found"**
   - Verify credentials in config files
   - Check for typos in IDs/secrets

3. **Database errors**
   - Run the migration script
   - Check database permissions

4. **404 errors on callback**
   - Verify URL routing configuration
   - Check file permissions

### Error Codes:
- `?error=z` - Zalo authentication failed
- `?error=d` - Discord authentication failed  
- `?error=b` - Account banned/disabled

## Security Considerations

1. **Keep secrets secure** - Never commit real credentials to version control
2. **Use HTTPS** - Required for production OAuth
3. **Validate redirects** - Ensure redirect URIs are whitelisted
4. **Monitor failed attempts** - Log and monitor authentication failures
5. **Regular updates** - Keep OAuth libraries and dependencies updated

## Support

For issues with this implementation:
1. Check the error logs
2. Verify OAuth app configurations
3. Test with development credentials first
4. Ensure database migrations completed successfully
