<?php
require_once($_SERVER['DOCUMENT_ROOT'] . '/config.php');
$expiration = time() + (604800 * 4 * 12); // 7 days * 4 weeks * 12 months

if (isset($_GET['code'])) {
    // Get access token from Discord
    $token_response = getDiscordAccessToken($_GET['code'], $discord_config);
    
    if (!$token_response || isset($token_response['error'])) {
        header('location:' . home_url() . '/customer/login?error=d');
        die();
    }
    
    $access_token = $token_response['access_token'];
    
    // Get user information from Discord
    $user_info = getDiscordUserInfo($access_token, $discord_config);
    
    if (!$user_info || isset($user_info['error'])) {
        header('location:' . home_url() . '/customer/login?error=d');
        die();
    }
    
    // Extract user information
    $name = $user_info['global_name'] ?? $user_info['username'] ?? 'Discord User';
    $email = $user_info['email'] ?? null;
    $discord_id = $user_info['id'];
    $avatar = $user_info['avatar'] ? 'https://cdn.discordapp.com/avatars/' . $discord_id . '/' . $user_info['avatar'] . '.png' : '';
    
    // If no email provided, generate one
    if (!$email) {
        $email = 'discord_' . $discord_id . '@discord.local';
    }
    
    // Check if user exists by Discord ID or email
    $check = $duogxaolin->getRow("SELECT * FROM `user` WHERE `discord_id` = ? OR `email` = ?", [$discord_id, $email]);
    
    require_once($_SERVER['DOCUMENT_ROOT'] . '/app/controllers/client/customer/device.php');
    
    if ($check) {
        // Existing user login
        if ($check['status'] != 1) {
            header('location:' . home_url() . '/customer/login?error=b&text=' . $check['log_msg']);
            die();
        }
        
        $token = password_hash($check['username'] . $check['password'] . md5(md5($userAgent)), PASSWORD_DEFAULT);
        $token = preg_replace('/[^a-zA-Z0-9]/', '', $token);

        $ct = $duogxaolin->insert("user_devices", [
            'device_id' => $device_id,
            'user_id' => $check['id'],
            'create_time' => time(),
            'update_time' => time(),
            'ip' => myip(),
            'status' => 1,
            'refresh_token' => $token,
            'password' => $check['password'],
            'browser' => $browser,
            'browser_ver' => $browser_ver,
            'os_name' => $os_name,
            'os_ver' => $os_ver,
            'device_manufacturer' => $device_manufacturer,
            'device_model' => $device_model,
            'device_identifier' => $device_identifier,
            'expiration' => $expiration,
            'login' => 'discord',
            'verify' => 0
        ]);

        // Update Discord ID if not set
        if (empty($check['discord_id'])) {
            $duogxaolin->update("user", [
                'discord_id' => $discord_id,
                'update_time' => time()
            ], " `id` = ? ", [$check['id']]);
        } else {
            $duogxaolin->update("user", [
                'update_time' => time()
            ], " `id` = ? ", [$check['id']]);
        }

        setcookie('session_token', $token, [
            'expires' => $expiration,
            'path' => '/',
            'secure' => true,
            'httponly' => true
        ]);
        $_SESSION['username'] = $check['username'];
        header('location:' . home_url());
        die();
    } else {
        // New user registration with improved username generation

        // Helper function to remove Vietnamese diacritics
        function removeVietnameseDiacritics($str) {
            $vietnamese = [
                'à', 'á', 'ạ', 'ả', 'ã', 'â', 'ầ', 'ấ', 'ậ', 'ẩ', 'ẫ', 'ă', 'ằ', 'ắ', 'ặ', 'ẳ', 'ẵ',
                'è', 'é', 'ẹ', 'ẻ', 'ẽ', 'ê', 'ề', 'ế', 'ệ', 'ể', 'ễ',
                'ì', 'í', 'ị', 'ỉ', 'ĩ',
                'ò', 'ó', 'ọ', 'ỏ', 'õ', 'ô', 'ồ', 'ố', 'ộ', 'ổ', 'ỗ', 'ơ', 'ờ', 'ớ', 'ợ', 'ở', 'ỡ',
                'ù', 'ú', 'ụ', 'ủ', 'ũ', 'ư', 'ừ', 'ứ', 'ự', 'ử', 'ữ',
                'ỳ', 'ý', 'ỵ', 'ỷ', 'ỹ',
                'đ',
                'À', 'Á', 'Ạ', 'Ả', 'Ã', 'Â', 'Ầ', 'Ấ', 'Ậ', 'Ẩ', 'Ẫ', 'Ă', 'Ằ', 'Ắ', 'Ặ', 'Ẳ', 'Ẵ',
                'È', 'É', 'Ẹ', 'Ẻ', 'Ẽ', 'Ê', 'Ề', 'Ế', 'Ệ', 'Ể', 'Ễ',
                'Ì', 'Í', 'Ị', 'Ỉ', 'Ĩ',
                'Ò', 'Ó', 'Ọ', 'Ỏ', 'Õ', 'Ô', 'Ồ', 'Ố', 'Ộ', 'Ổ', 'Ỗ', 'Ơ', 'Ờ', 'Ớ', 'Ợ', 'Ở', 'Ỡ',
                'Ù', 'Ú', 'Ụ', 'Ủ', 'Ũ', 'Ư', 'Ừ', 'Ứ', 'Ự', 'Ử', 'Ữ',
                'Ỳ', 'Ý', 'Ỵ', 'Ỷ', 'Ỹ',
                'Đ'
            ];

            $latin = [
                'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a',
                'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e',
                'i', 'i', 'i', 'i', 'i',
                'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o',
                'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u',
                'y', 'y', 'y', 'y', 'y',
                'd',
                'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A',
                'E', 'E', 'E', 'E', 'E', 'E', 'E', 'E', 'E', 'E', 'E',
                'I', 'I', 'I', 'I', 'I',
                'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O',
                'U', 'U', 'U', 'U', 'U', 'U', 'U', 'U', 'U', 'U', 'U',
                'Y', 'Y', 'Y', 'Y', 'Y',
                'D'
            ];

            return str_replace($vietnamese, $latin, $str);
        }

        // Generate username from display name (prefer global_name over username)
        $display_name = $name; // Use the display name from Discord

        // Remove Vietnamese diacritics
        $username = removeVietnameseDiacritics($display_name);

        // Convert to lowercase and remove special characters/spaces
        $username = strtolower($username);
        $username = preg_replace('/[^a-z0-9]/', '', $username);

        // Ensure minimum length
        if (strlen($username) < 3) {
            $username = 'discorduser' . substr($discord_id, -3);
        }

        // Check for uniqueness and handle duplicates
        $original_username = $username;
        $attempt = 0;

        while ($duogxaolin->getRow("SELECT * FROM `user` WHERE `username` = ?", [$username])) {
            $attempt++;

            // Extract 1-3 random digits from Discord ID
            $id_digits = preg_replace('/[^0-9]/', '', $discord_id);
            if (strlen($id_digits) >= 3) {
                $random_start = rand(0, strlen($id_digits) - 3);
                $suffix = substr($id_digits, $random_start, 3);
            } else {
                $suffix = str_pad($id_digits, 3, '0', STR_PAD_LEFT);
            }

            $username = $original_username . $suffix;

            // Fallback: if still not unique after several attempts
            if ($attempt > 10) {
                $username = $original_username . rand(100, 999);
                break;
            }
        }
        
        $password_default = random('qwertyuiopasdfghjkcvbnm1234567890', 6);
        $password = md5(md5($password_default));
        $token = password_hash($username . $password . md5(md5($userAgent)), PASSWORD_DEFAULT);
        $token = preg_replace('/[^a-zA-Z0-9]/', '', $token);

        $create = $duogxaolin->insert("user", [
            'username' => $username,
            'fullname' => $name,
            'email' => $email,
            'token' => password_hash($password, PASSWORD_DEFAULT),
            'password' => password_hash($password, PASSWORD_DEFAULT),
            'money' => 0,
            'used_money' => 0,
            'verify_email' => 1, // Auto-verify for social login
            'total_money' => 0,
            'status' => 1,
            'ip' => myip(),
            'create_time' => time(),
            'update_time' => time(),
            'discord_id' => $discord_id
        ]);
        
        if (!$create) {
            die('error creating');
        } else {
            $row = $duogxaolin->getRow("SELECT * FROM `user` WHERE `username` = ?", [$username]);
            if (!$row) {
                die(json_encode([
                    'status' => 'error',
                    'msg' => 'Đăng ký thất bại !'
                ]));
            }
            $last_id = $row['id'];
        }

        $ct = $duogxaolin->insert("user_devices", [
            'device_id' => $device_id,
            'user_id' => $row['id'],
            'create_time' => time(),
            'update_time' => time(),
            'ip' => myip(),
            'status' => 1,
            'refresh_token' => $token,
            'password' => password_hash($password, PASSWORD_DEFAULT),
            'browser' => $browser,
            'browser_ver' => $browser_ver,
            'os_name' => $os_name,
            'os_ver' => $os_ver,
            'device_manufacturer' => $device_manufacturer,
            'device_model' => $device_model,
            'device_identifier' => $device_identifier,
            'expiration' => $expiration,
            'login' => 'discord',
            'verify' => 0
        ]);
        
        // Send welcome email
        $form_mail = $duogxaolin->getRow("SELECT * FROM `mail` WHERE `key` = ?", ['register']);
        if ($form_mail) {
            if ($form_mail['value'] != null) {
                $subject = $form_mail['sub'];
                $content = $form_mail['value'];
                $content = str_replace("{gettime}", gettime(), $content);
                $content = str_replace("{fullname}", $name, $content);
                $content = str_replace("{email}", $email, $content);
                $content = str_replace("{username}", $username, $content);
                $duogxaolin->insert("notification", [
                   'subject' => $subject,
                    'content' => $content,
                    'type' => 'email',
                    'email' => $email,
                    'status' => 0,
                    'create_time' => time(),
                    'update_time' => time(),
                ]);
            }
        }
        
        // Set cookie and session
        setcookie('session_token', $token, [
            'expires' => $expiration,
            'path' => '/',
            'secure' => true,
            'httponly' => true
        ]);
        $_SESSION['username'] = $row['username'];
        header('location:' . home_url());
        die();
    }
}
