<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gaming Pagination Test</title>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        body {
            font-family: 'Montserrat', sans-serif;
            background: linear-gradient(135deg, #0E0A2F 0%, #1a1640 50%, #0E0A2F 100%);
            min-height: 100vh;
        }

        /* Gaming Pagination Styles */
        .gaming-pagination-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 1rem;
            padding: 1.5rem;
            background: linear-gradient(145deg, rgba(19, 17, 46, 0.8) 0%, rgba(30, 41, 59, 0.6) 50%, rgba(19, 17, 46, 0.8) 100%);
            border: 1px solid rgba(80, 129, 255, 0.2);
            border-radius: 1rem;
            backdrop-filter: blur(10px);
            box-shadow: 
                0 8px 32px rgba(0, 0, 0, 0.3),
                0 0 20px rgba(80, 129, 255, 0.1);
        }

        .gaming-pagination-list {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .gaming-pagination-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 0.5rem;
            min-width: 2.5rem;
            height: 2.5rem;
            padding: 0.5rem 0.75rem;
            border: none;
            border-radius: 0.5rem;
            font-family: 'Montserrat', sans-serif;
            font-size: 0.875rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
            position: relative;
            overflow: hidden;
        }

        .gaming-pagination-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s ease;
        }

        .gaming-pagination-btn:hover::before {
            left: 100%;
        }

        .gaming-pagination-btn--number {
            background: linear-gradient(145deg, rgba(39, 36, 80, 0.8) 0%, rgba(30, 41, 59, 0.6) 100%);
            color: #FFFFFF99;
            border: 1px solid rgba(80, 129, 255, 0.3);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
        }

        .gaming-pagination-btn--number:hover {
            background: linear-gradient(145deg, rgba(80, 129, 255, 0.2) 0%, rgba(75, 125, 255, 0.3) 100%);
            color: #FFFFFF;
            border-color: rgba(80, 129, 255, 0.6);
            transform: translateY(-2px) scale(1.05);
            box-shadow: 
                0 8px 16px rgba(80, 129, 255, 0.3),
                0 0 20px rgba(80, 129, 255, 0.2);
        }

        .gaming-pagination-btn--active {
            background: linear-gradient(135deg, #4B7DFF 0%, #5081FF 50%, #3463DB 100%);
            color: #FFFFFF;
            border: 1px solid rgba(80, 129, 255, 0.8);
            box-shadow: 
                0 4px 12px rgba(80, 129, 255, 0.4),
                0 0 20px rgba(80, 129, 255, 0.3),
                inset 0 1px 0 rgba(255, 255, 255, 0.2);
            animation: gaming-pagination-pulse 2s infinite;
        }

        .gaming-pagination-btn--nav {
            background: linear-gradient(145deg, rgba(75, 125, 255, 0.1) 0%, rgba(80, 129, 255, 0.2) 100%);
            color: #5081FF;
            border: 1px solid rgba(80, 129, 255, 0.4);
            min-width: 3rem;
        }

        .gaming-pagination-btn--nav:hover {
            background: linear-gradient(145deg, rgba(75, 125, 255, 0.3) 0%, rgba(80, 129, 255, 0.4) 100%);
            color: #FFFFFF;
            border-color: rgba(80, 129, 255, 0.8);
            transform: translateY(-2px) scale(1.05);
            box-shadow: 
                0 8px 16px rgba(80, 129, 255, 0.3),
                0 0 20px rgba(80, 129, 255, 0.2);
        }

        .gaming-pagination-btn--disabled {
            background: linear-gradient(145deg, rgba(39, 36, 80, 0.3) 0%, rgba(30, 41, 59, 0.2) 100%);
            color: #FFFFFF33;
            border: 1px solid rgba(80, 129, 255, 0.1);
            cursor: not-allowed;
            opacity: 0.5;
        }

        .gaming-pagination-ellipsis {
            display: flex;
            align-items: center;
            justify-content: center;
            min-width: 2.5rem;
            height: 2.5rem;
            color: #FFFFFF66;
            font-size: 1rem;
        }

        .gaming-pagination-icon {
            flex-shrink: 0;
            transition: transform 0.3s ease;
        }

        .gaming-pagination-btn:hover .gaming-pagination-icon {
            transform: scale(1.1);
        }

        .gaming-pagination-text {
            font-size: 0.875rem;
            font-weight: 600;
        }

        .gaming-pagination-info {
            text-align: center;
            margin-top: 0.5rem;
        }

        .gaming-pagination-info-text {
            font-size: 0.875rem;
            color: #FFFFFF99;
            font-family: 'Montserrat', sans-serif;
        }

        .gaming-pagination-info-current,
        .gaming-pagination-info-total {
            font-weight: 700;
            color: #5081FF;
            text-shadow: 0 0 8px rgba(80, 129, 255, 0.5);
        }

        @keyframes gaming-pagination-pulse {
            0%, 100% {
                box-shadow: 
                    0 4px 12px rgba(80, 129, 255, 0.4),
                    0 0 20px rgba(80, 129, 255, 0.3),
                    inset 0 1px 0 rgba(255, 255, 255, 0.2);
            }
            50% {
                box-shadow: 
                    0 6px 16px rgba(80, 129, 255, 0.6),
                    0 0 30px rgba(80, 129, 255, 0.5),
                    inset 0 1px 0 rgba(255, 255, 255, 0.3);
            }
        }

        /* Improved Mobile Responsive Design */
        @media (max-width: 768px) {
            .gaming-pagination-container {
                padding: 1.25rem 1rem;
                gap: 1rem;
                margin: 0 0.5rem;
            }

            .gaming-pagination-list {
                gap: 0.5rem;
                flex-wrap: wrap;
                justify-content: center;
                align-items: center;
            }

            .gaming-pagination-btn {
                min-width: 2.75rem;
                height: 2.75rem;
                padding: 0.5rem 0.75rem;
                font-size: 0.875rem;
                border-radius: 0.75rem;
            }

            .gaming-pagination-btn--nav {
                min-width: 3.25rem;
                padding: 0.5rem 0.875rem;
            }

            .gaming-pagination-text {
                font-size: 0.8125rem;
                font-weight: 600;
            }

            .gaming-pagination-ellipsis {
                min-width: 2.75rem;
                height: 2.75rem;
                margin: 0 0.25rem;
            }
        }

        @media (max-width: 640px) {
            .gaming-pagination-container {
                padding: 1rem 0.75rem;
                gap: 0.875rem;
                margin: 0 0.25rem;
            }

            .gaming-pagination-list {
                gap: 0.375rem;
                max-width: 100%;
                overflow-x: auto;
                padding: 0.25rem 0;
                scrollbar-width: none;
                -ms-overflow-style: none;
            }

            .gaming-pagination-list::-webkit-scrollbar {
                display: none;
            }

            .gaming-pagination-btn {
                min-width: 2.5rem;
                height: 2.5rem;
                padding: 0.375rem 0.625rem;
                font-size: 0.8125rem;
                flex-shrink: 0;
            }

            .gaming-pagination-btn--nav {
                min-width: 2.875rem;
                padding: 0.375rem 0.75rem;
            }

            .gaming-pagination-ellipsis {
                min-width: 2.5rem;
                height: 2.5rem;
            }
        }

        @media (max-width: 480px) {
            .gaming-pagination-container {
                padding: 0.875rem 0.5rem;
                gap: 0.75rem;
                margin: 0;
                border-radius: 0.75rem;
            }

            .gaming-pagination-list {
                gap: 0.25rem;
                justify-content: flex-start;
                padding: 0.125rem 0;
            }

            .gaming-pagination-text {
                display: none !important;
            }

            .gaming-pagination-btn {
                min-width: 2.25rem;
                height: 2.25rem;
                padding: 0.25rem;
                font-size: 0.75rem;
                border-radius: 0.5rem;
                flex-shrink: 0;
            }

            .gaming-pagination-btn--nav {
                min-width: 2.5rem;
                padding: 0.25rem 0.375rem;
            }

            .gaming-pagination-ellipsis {
                min-width: 2.25rem;
                height: 2.25rem;
                font-size: 0.875rem;
            }

            .gaming-pagination-icon {
                width: 16px;
                height: 16px;
            }
        }

        @media (max-width: 360px) {
            .gaming-pagination-container {
                padding: 0.75rem 0.375rem;
                gap: 0.625rem;
            }

            .gaming-pagination-list {
                gap: 0.125rem;
                padding: 0;
            }

            .gaming-pagination-btn {
                min-width: 2rem;
                height: 2rem;
                padding: 0.125rem;
                font-size: 0.6875rem;
            }

            .gaming-pagination-btn--nav {
                min-width: 2.25rem;
                padding: 0.125rem 0.25rem;
            }

            .gaming-pagination-ellipsis {
                min-width: 2rem;
                height: 2rem;
                font-size: 0.75rem;
            }

            .gaming-pagination-icon {
                width: 14px;
                height: 14px;
            }
        }
    </style>
</head>
<body class="text-white">
    <div class="container mx-auto px-4 py-8">
        <h1 class="text-3xl font-bold text-center mb-8 text-[#5081FF]">Gaming Pagination Test</h1>
        
        <div class="mb-8 text-center">
            <p class="text-lg mb-4">Test the new gaming-themed pagination component:</p>
            <ul class="text-sm space-y-2 text-[#FFFFFF99]">
                <li>• <strong>Gaming colors:</strong> #4B7DFF, #5081FF gradients</li>
                <li>• <strong>Hover effects:</strong> Scale, glow, and smooth transitions</li>
                <li>• <strong>Active state:</strong> Pulsing animation for current page</li>
                <li>• <strong>Responsive design:</strong> Adapts to mobile and desktop</li>
                <li>• <strong>Accessibility:</strong> Focus states and reduced motion support</li>
            </ul>
        </div>

        <!-- Gaming Pagination Demo -->
        <div x-data="paginationDemo()" class="space-y-8">
            <!-- Demo Controls -->
            <div class="text-center space-y-4">
                <div class="space-x-2 space-y-2 flex flex-wrap justify-center">
                    <button @click="setTotalPages(3)" class="bg-[#4B7DFF] hover:bg-[#5081FF] px-3 py-2 rounded transition-colors text-sm">3 Pages</button>
                    <button @click="setTotalPages(5)" class="bg-[#4B7DFF] hover:bg-[#5081FF] px-3 py-2 rounded transition-colors text-sm">5 Pages</button>
                    <button @click="setTotalPages(10)" class="bg-[#4B7DFF] hover:bg-[#5081FF] px-3 py-2 rounded transition-colors text-sm">10 Pages</button>
                    <button @click="setTotalPages(20)" class="bg-[#4B7DFF] hover:bg-[#5081FF] px-3 py-2 rounded transition-colors text-sm">20 Pages</button>
                    <button @click="setTotalPages(50)" class="bg-[#4B7DFF] hover:bg-[#5081FF] px-3 py-2 rounded transition-colors text-sm">50 Pages</button>
                    <button @click="setTotalPages(100)" class="bg-[#4B7DFF] hover:bg-[#5081FF] px-3 py-2 rounded transition-colors text-sm">100 Pages</button>
                </div>
                <div class="space-x-2 space-y-2 flex flex-wrap justify-center">
                    <button @click="goToPage(1)" class="bg-[#10B981] hover:bg-[#059669] px-3 py-1 rounded transition-colors text-sm">Go to First</button>
                    <button @click="goToPage(Math.ceil(totalPages/2))" class="bg-[#10B981] hover:bg-[#059669] px-3 py-1 rounded transition-colors text-sm">Go to Middle</button>
                    <button @click="goToPage(totalPages)" class="bg-[#10B981] hover:bg-[#059669] px-3 py-1 rounded transition-colors text-sm">Go to Last</button>
                </div>
                <p class="text-sm text-[#FFFFFF99]">Test different scenarios: <strong>Max 7 total elements</strong> with <strong>first & last page always visible</strong></p>
                <div class="mt-2 text-xs text-[#FFFFFF66]">
                    <p>• Total elements = Previous + Page Numbers + Next ≤ 7</p>
                    <p>• Page 1 and last page are ALWAYS visible</p>
                    <p>• Smart ellipsis placement between first/middle/last sections</p>
                </div>
            </div>

            <!-- Gaming Pagination Component -->
            <template x-if="totalPages > 0">
                <div class="flex w-full flex-col justify-center mt-8 mb-6">
                    <div class="gaming-pagination-container mx-auto">
                        <ul class="gaming-pagination-list">
                            <!-- Previous Button -->
                            <li>
                                <button @click="goToPage(currentPage - 1)"
                                        :disabled="currentPage === 1"
                                        :class="currentPage === 1 ? 'gaming-pagination-btn gaming-pagination-btn--disabled' : 'gaming-pagination-btn gaming-pagination-btn--nav'"
                                        class="gaming-pagination-prev">
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="gaming-pagination-icon">
                                        <path d="M15 5L9 12L15 19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                                    </svg>
                                    <span class="gaming-pagination-text hidden sm:inline">Trước</span>
                                </button>
                            </li>

                            <!-- Page Number Buttons -->
                            <template x-for="(page, index) in pages" :key="index">
                                <li>
                                    <template x-if="page === '...'">
                                        <span class="gaming-pagination-ellipsis">
                                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                <circle cx="5" cy="12" r="2" fill="currentColor"></circle>
                                                <circle cx="12" cy="12" r="2" fill="currentColor"></circle>
                                                <circle cx="19" cy="12" r="2" fill="currentColor"></circle>
                                            </svg>
                                        </span>
                                    </template>
                                    <template x-if="page !== '...'">
                                        <button @click="goToPage(page)"
                                                x-text="page"
                                                :class="page === currentPage ? 'gaming-pagination-btn gaming-pagination-btn--active' : 'gaming-pagination-btn gaming-pagination-btn--number'">
                                        </button>
                                    </template>
                                </li>
                            </template>

                            <!-- Next Button -->
                            <li>
                                <button @click="goToPage(currentPage + 1)"
                                        :disabled="currentPage === totalPages"
                                        :class="currentPage === totalPages ? 'gaming-pagination-btn gaming-pagination-btn--disabled' : 'gaming-pagination-btn gaming-pagination-btn--nav'"
                                        class="gaming-pagination-next">
                                    <span class="gaming-pagination-text hidden sm:inline">Sau</span>
                                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="gaming-pagination-icon">
                                        <path d="M9 5L15 12L9 19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                                    </svg>
                                </button>
                            </li>
                        </ul>

                        <!-- Gaming Pagination Info -->
                        <div class="gaming-pagination-info">
                            <span class="gaming-pagination-info-text">
                                Trang <span class="gaming-pagination-info-current" x-text="currentPage"></span> 
                                trên <span class="gaming-pagination-info-total" x-text="totalPages"></span>
                            </span>
                        </div>
                    </div>
                </div>
            </template>

            <!-- Debug Info -->
            <div class="bg-gray-800 p-4 rounded text-sm">
                <h3 class="font-bold mb-2">Debug Information:</h3>
                <p>Current Page: <span x-text="currentPage" class="text-[#5081FF]"></span></p>
                <p>Total Pages: <span x-text="totalPages" class="text-[#5081FF]"></span></p>
                <p>Pages Array: <span x-text="JSON.stringify(pages)" class="text-[#5081FF]"></span></p>
                <p>Page Elements: <span x-text="pages.length" class="text-[#5081FF]"></span></p>
                <p>Total Elements: <span x-text="pages.length + 2" class="text-[#5081FF]"></span> (including Prev/Next)</p>
                <p class="mt-2 text-xs" :class="pages.length + 2 <= 7 ? 'text-green-400' : 'text-red-400'">
                    <span x-text="pages.length + 2 <= 7 ? '✓ Within 7-element limit' : '✗ Exceeds 7-element limit'"></span>
                </p>
            </div>
        </div>
    </div>

    <script>
        function paginationDemo() {
            return {
                currentPage: 1,
                totalPages: 10,
                pages: [],

                init() {
                    this.buildPages();
                },

                setTotalPages(total) {
                    this.totalPages = total;
                    this.currentPage = 1;
                    this.buildPages();
                },

                goToPage(page) {
                    if (typeof page !== 'number' || isNaN(page)) return;
                    if (page < 1 || page > this.totalPages) return;
                    this.currentPage = page;
                    this.buildPages();
                },

                buildPages() {
                    this.pages = [];
                    const total = this.totalPages;
                    const current = this.currentPage;

                    console.log('Building pages - Current:', current, 'Total:', total);

                    // Handle edge cases
                    if (total <= 0) return;
                    if (total === 1) {
                        this.pages.push(1);
                        return;
                    }

                    // Calculate available slots for page numbers
                    // Total elements = Prev + Page Numbers + Next = 7 max
                    // So we have 5 slots for page numbers and ellipsis (7 - 2 navigation buttons)
                    const maxPageSlots = 5;

                    // For small page counts (≤5), show all pages (no ellipsis needed)
                    if (total <= maxPageSlots) {
                        for (let i = 1; i <= total; i++) {
                            this.pages.push(i);
                        }
                        return;
                    }

                    // For larger page counts, ALWAYS show first and last page
                    // Strategy: [1] [...] [middle pages] [...] [total] within 5 slots

                    // Always start with page 1
                    this.pages.push(1);

                    // Determine middle section based on current page position
                    if (current <= 4) {
                        // Current page is near the beginning (1, 2, 3, or 4)
                        // Pattern: [1] [2] [3] [4] [...] [total] = 6 elements
                        // Or: [1] [2] [3] [...] [total] = 5 elements
                        const endRange = Math.min(4, total - 2); // Leave room for ellipsis and last page
                        for (let i = 2; i <= endRange; i++) {
                            this.pages.push(i);
                        }

                        // Add ellipsis if there's a gap before the last page
                        if (endRange < total - 1) {
                            this.pages.push('...');
                        }
                    } else if (current >= total - 3) {
                        // Current page is near the end (last-3, last-2, last-1, or last)
                        // Pattern: [1] [...] [total-3] [total-2] [total-1] [total] = 6 elements
                        // Or: [1] [...] [total-2] [total-1] [total] = 5 elements

                        // Add ellipsis after page 1 if there's a gap
                        if (total > 6) {
                            this.pages.push('...');
                        }

                        // Add the last few pages before the final page
                        const startRange = Math.max(total - 3, 2); // Start from at least page 2
                        for (let i = startRange; i < total; i++) {
                            if (!this.pages.includes(i)) {
                                this.pages.push(i);
                            }
                        }
                    } else {
                        // Current page is in the middle
                        // Pattern: [1] [...] [current-1] [current] [current+1] [...] [total] = 7 elements
                        // But we only have 5 slots, so optimize: [1] [...] [current] [...] [total] = 5 elements

                        // Add ellipsis after page 1
                        this.pages.push('...');

                        // Add current page (and one neighbor if we have space)
                        if (this.pages.length < maxPageSlots - 2) { // Reserve space for ellipsis and last page
                            this.pages.push(current - 1);
                        }
                        this.pages.push(current);
                        if (this.pages.length < maxPageSlots - 2) { // Reserve space for ellipsis and last page
                            this.pages.push(current + 1);
                        }

                        // Add ellipsis before last page if there's a gap
                        if (current + 1 < total - 1 || (this.pages.length === maxPageSlots - 2 && current < total - 1)) {
                            this.pages.push('...');
                        }
                    }

                    // Always end with the last page (if not already included)
                    if (!this.pages.includes(total)) {
                        this.pages.push(total);
                    }

                    // Safety check: ensure we never exceed 5 page elements (leaving 2 for prev/next)
                    if (this.pages.length > maxPageSlots) {
                        console.warn('Page array exceeds maximum slots, truncating:', this.pages);
                        // Keep first page, last page, and middle elements
                        const firstPage = this.pages[0];
                        const lastPage = this.pages[this.pages.length - 1];
                        const middleElements = this.pages.slice(1, -1).slice(0, maxPageSlots - 2);
                        this.pages = [firstPage, ...middleElements, lastPage];
                    }

                    console.log('Generated pages array (always shows first & last):', this.pages);
                    console.log('Total elements will be:', this.pages.length + 2, '(including prev/next)');
                }
            }
        }
    </script>
</body>
</html>
