<?php
    require_once $_SERVER['DOCUMENT_ROOT'] . '/config.php';
    $code1 = xss($_GET['game']);
    $code2 = xss($_GET['category']);
    $game = $duogxaolin->getRow("SELECT `id`, `server`, `rank`, `level`, `char`, `weapon`, `skin`, `attribute_4`, `attribute_5` 
    FROM `game` WHERE `slug` = ? "
    ,[$code1]
    );
    if (! $game) {
        require_once $_SERVER['DOCUMENT_ROOT'] . '/404.php';
        die();
    }
    $service = $duogxaolin->getRow("SELECT `id`, `name`, `type`, `note` 
    FROM `category` WHERE `slug` = ? AND `game_id` = ?"
    ,[$code2,$game['id']]
    );
    if (! $service) {
        require_once $_SERVER['DOCUMENT_ROOT'] . '/404.php';
        die();
    }
    if ($service['type'] != 'muanick') {
        require_once $_SERVER['DOCUMENT_ROOT'] . '/404.php';
        die();
    }
    // Safely get GET parameters with default values to prevent undefined array key warnings
    $keyword = isset($_GET['keyword']) ? check_string($_GET['keyword']) : '';
    $rate    = isset($_GET['rate']) ? check_string($_GET['rate']) : '';
    $sort    = isset($_GET['sort']) ? check_string($_GET['sort']) : '';
    $level   = isset($_GET['level']) ? check_string($_GET['level']) : '';
    $server  = isset($_GET['server']) ? check_string($_GET['server']) : '';
    $rank    = isset($_GET['rank']) ? check_string($_GET['rank']) : '';
    $attribute_4 = isset($_GET['attribute_4']) ? check_string($_GET['attribute_4']) : '';
    $attribute_5 = isset($_GET['attribute_5']) ? check_string($_GET['attribute_5']) : '';
    $shop    = isset($_GET['shop']) ? check_string($_GET['shop']) : '';

    // Create filters array for use in the form
    $filters = [
        'keyword' => $keyword,
        'rate' => $rate,
        'sort' => $sort,
        'level' => $level,
        'server' => $server,
        'rank' => $rank,
        'attribute_4' => $attribute_4,
        'attribute_5' => $attribute_5,
        'shop' => $shop
    ];
    
    // Lấy các giá trị từ GET theo key (lưu ý: nếu form gửi tên là char_map[] thì key sẽ là "char_map")
    $charValue = getFilterValues('char_map');
    $weapValue = getFilterValues('weap_map');
    $skinValue = getFilterValues('skin_map');

    // Hàm xử lý dữ liệu cho các thuộc tính
    $char_datas = processData($game['id'], 'char', $charValue);
    $weap_datas = processData($game['id'], 'weapon', $weapValue);
    $skin_datas = processData($game['id'], 'skin', $skinValue);
    // Xây dựng câu truy vấn SQL động với các bộ lọc
    require_once $_SERVER['DOCUMENT_ROOT'] . '/public/includes/header.php';
    require_once $_SERVER['DOCUMENT_ROOT'] . '/public/includes/navbar.php';
?>

<main class="flex-1 overflow-hidden relative z-[1]">
        <div class="container md:px-[32px] lg:px-[16px]">
        <div class="my-[36px] mt-[24px] max-md:mt-0">
          <div>
  <div class="text-center">
    <h4 class="font-bold text-white text-3xl uppercase"
        style="text-shadow: rgb(70, 155, 255) 0px 0px 8px, rgb(70, 155, 255) 0px 0px 8px, rgb(70, 155, 255) 0px 0px 40px;">
        <?php echo $service['name'] ?>
    </h4>
    <?php if($service['note'] != ''): ?>
      <div class="bg-success-light border-success  dark:bg-success-dark-light my-5 text-sm px-3 py-5">
    <?php echo $service['note'] ?>
   </div>
    <div class="flex justify-center pt-3">
      <div class="w-1/6 border-b-2 border-mandy-500"></div>
    </div>
    <?php endif; ?>
  </div>
  <!-- Gaming-Style Compact Filter Section -->
  <div class="mb-6">
    <!-- Compact Filter Toggle Button -->
    <div class="flex items-center justify-between mb-4">
      <h3 class="text-xl font-bold text-gaming-blue flex items-center gap-2">
        <i class="fas fa-filter text-gaming-blue"></i>
        Bộ Lọc Tìm Kiếm
      </h3>
      <button id="toggleSearchButton"
              x-data="{ expanded: false }"
              @click="expanded = !expanded; document.getElementById('searchCard').style.display = expanded ? 'block' : 'none'"
              class="gaming-btn-secondary px-4 py-2 text-sm lg:hidden">
        <i class="fas fa-chevron-down"
           :class="expanded ? 'rotate-180' : ''"></i>
        <span class="ml-2">Hiện/Ẩn Bộ Lọc</span>
      </button>
    </div>

    <!-- Gaming-Style Filter Container -->
    <div class="gaming-card-hover p-4 lg:p-6 mb-6">
      <form id="searchCard" class="hidden lg:block">
        <!-- Filter Grid Layout -->
        <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          <input type="hidden" name="shop" value="<?php echo $filters['shop'] ?>" />

          <?php if ($game['char'] != ''): ?>
          <div class="group col-span-1">
            <label for="char_map" class="block text-sm font-semibold text-gaming-blue mb-2">
              <i class="fas fa-user-ninja mr-2"></i><?php echo $game['char'] ?>
            </label>
            <select id="char_map" name="char_map[]" class="gaming-select select2" multiple>
              <?php foreach ($char_datas as $char): ?>
                <option value="<?php echo htmlspecialchars($char['id']); ?>"
                        <?php echo $char['selected'] ? 'selected' : ''; ?>>
                  <?php echo htmlspecialchars($char['text']); ?>
                </option>
              <?php endforeach; ?>
            </select>
          </div>
          <?php endif; ?>

          <?php if ($game['weapon'] != ''): ?>
          <div class="group col-span-1">
            <label for="weap_map" class="block text-sm font-semibold text-gaming-blue mb-2">
              <i class="fas fa-sword mr-2"></i><?php echo $game['weapon'] ?>
            </label>
            <select id="weap_map" name="weap_map[]" class="gaming-select select2" multiple>
              <?php foreach ($weap_datas as $weap): ?>
                <option value="<?php echo htmlspecialchars($weap['id']); ?>"
                        <?php echo $weap['selected'] ? 'selected' : ''; ?>>
                  <?php echo htmlspecialchars($weap['text']); ?>
                </option>
              <?php endforeach; ?>
            </select>
          </div>
          <?php endif; ?>

          <?php if ($game['skin'] != ''): ?>
          <div class="group col-span-1">
            <label for="skin_map" class="block text-sm font-semibold text-gaming-blue mb-2">
              <i class="fas fa-palette mr-2"></i><?php echo $game['skin'] ?>
            </label>
            <select id="skin_map" name="skin_map[]" class="gaming-select select2" multiple>
              <?php foreach ($skin_datas as $skin): ?>
                <option value="<?php echo htmlspecialchars($skin['id']); ?>"
                        <?php echo $skin['selected'] ? 'selected' : ''; ?>>
                  <?php echo htmlspecialchars($skin['text']); ?>
                </option>
              <?php endforeach; ?>
            </select>
          </div>
          <?php endif; ?>
          <?php if ($game['server'] != ''): ?>
          <div class="group col-span-1">
            <label for="server" class="block text-sm font-semibold text-gaming-blue mb-2">
              <i class="fas fa-server mr-2"></i><?php echo $game['server'] ?>
            </label>
            <select class="gaming-select" name="server" id="server">
              <option value="">Chọn <?php echo $game['server'] ?></option>
              <?php foreach ($duogxaolin->getList("SELECT * FROM `game_attribute`
              WHERE `game_id` = ? AND `name` = ?"
              ,[$game['id'],'server']
              ) as $sv) {
                      $svcode = $sv['slug'];
                  ?>
              <option <?php echo $server == $svcode ? 'selected' : '' ?> value="<?php echo $sv['slug'] ?>">
                <?php echo $sv['value'] ?>
              </option>
              <?php }?>
            </select>
          </div>
          <?php endif; ?>

          <?php if ($game['rank'] != ''): ?>
          <div class="group col-span-1">
            <label for="rank" class="block text-sm font-semibold text-gaming-blue mb-2">
              <i class="fas fa-trophy mr-2"></i><?php echo $game['rank'] ?>
            </label>
            <select class="gaming-select" name="rank" id="rank">
              <option value="">Chọn <?php echo $game['rank'] ?></option>
              <?php foreach ($duogxaolin->getList("SELECT * FROM `game_attribute`
              WHERE `game_id` =? AND `name` = ?"
              ,[$game['id'],'rank']
              ) as $sv) {
                      $svcode = $sv['slug'];
                  ?>
              <option <?php echo $rank == $svcode ? 'selected' : '' ?> value="<?php echo $sv['slug'] ?>">
                <?php echo $sv['value'] ?>
              </option>
              <?php }?>
            </select>
          </div>
          <?php endif; ?>

          <?php if ($game['level'] != ''): ?>
          <div class="group col-span-1">
            <label for="level" class="block text-sm font-semibold text-gaming-blue mb-2">
              <i class="fas fa-level-up-alt mr-2"></i><?php echo $game['level'] ?>
            </label>
            <input type="search" value="<?php echo $level ?>"
              placeholder="<?php echo $game['level'] ?>,..." name="level" id="level" class="gaming-input" />
          </div>
          <?php endif; ?>

          <?php if ($game['attribute_4'] != ''): ?>
          <div class="group col-span-1">
            <label for="attribute_4" class="block text-sm font-semibold text-gaming-blue mb-2">
              <i class="fas fa-star mr-2"></i><?php echo $game['attribute_4'] ?>
            </label>
            <input type="search" value="<?php echo $attribute_4 ?>"
              placeholder="<?php echo $game['attribute_4'] ?>,..." name="attribute_4" id="attribute_4" class="gaming-input" />
          </div>
          <?php endif; ?>

          <?php if ($game['attribute_5'] != ''): ?>
          <div class="group col-span-1">
            <label for="attribute_5" class="block text-sm font-semibold text-gaming-blue mb-2">
              <i class="fas fa-gem mr-2"></i><?php echo $game['attribute_5'] ?>
            </label>
            <input type="search" value="<?php echo $attribute_5 ?>"
              placeholder="<?php echo $game['attribute_5'] ?>,..." name="attribute_5" id="attribute_5" class="gaming-input" />
          </div>
          <?php endif; ?>
          <div class="group col-span-1">
            <label for="sort" class="block text-sm font-semibold text-gaming-blue mb-2">
              <i class="fas fa-sort mr-2"></i>Sắp Xếp
            </label>
            <select class="gaming-select" name="sort" id="sort">
              <option <?php echo $sort == 'oldest' ? 'selected' : '' ?> value="oldest">Cũ nhất</option>
              <option <?php echo $sort == '' ? 'selected' : '' ?> value="">Mới nhất</option>
              <option <?php echo $sort == 'price_asc' ? 'selected' : '' ?> value="price_asc">Giá thấp đến cao</option>
              <option <?php echo $sort == 'price_desc' ? 'selected' : '' ?> value="price_desc">Giá cao đến thấp</option>
            </select>
          </div>

          <div class="group col-span-1">
            <label for="rate" class="block text-sm font-semibold text-gaming-blue mb-2">
              <i class="fas fa-dollar-sign mr-2"></i>Mức Giá
            </label>
            <select class="gaming-select" name="rate" id="rate">
              <option value="">Tất cả</option>
              <?php foreach ($duogxaolin->getList("SELECT * FROM `price_level` ORDER BY `min` ASC ",[]) as $cate) {
                      $rt = $cate['min'] . '-' . $cate['max'];
                  ?>
              <option <?php echo $rate == $rt ? 'selected' : '' ?> value="<?php echo $rt ?>">
                <?php echo $cate['name'] ?>
              </option>
              <?php }?>
            </select>
          </div>

          <div class="group col-span-1">
            <label for="keyword" class="block text-sm font-semibold text-gaming-blue mb-2">
              <i class="fas fa-search mr-2"></i>Mã Tài Khoản
            </label>
            <input type="search" value="<?php echo $keyword ?>"
              placeholder="Nhập mã tài khoản..." name="keyword" id="keyword" class="gaming-input" />
          </div>

          <!-- Search Button - Full Width on Mobile, Auto Width on Desktop -->
          <div class="col-span-full lg:col-span-1 flex items-end">
            <button type="submit" class="gaming-action-btn group/btn relative w-full bg-gradient-to-r from-[#4B7DFF] via-[#5081FF] to-[#4B7DFF] hover:from-[#5081FF] hover:via-[#4B7DFF] hover:to-[#5081FF] text-white font-bold py-3 px-4 rounded-lg transition-all duration-500 transform hover:scale-[1.02] hover:shadow-xl hover:shadow-[#4B7DFF]/30 border border-[#5081FF]/50 hover:border-[#4B7DFF]/80 overflow-hidden">
              <i class="fas fa-search mr-2"></i>
              <span>TÌM KIẾM</span>
            </button>
          </div>
        </div>
        </form>
      </div>
    </div>
    <div class="w-full">
      <!-- Phần hiển thị danh sách tài khoản được load bằng Alpine.js -->
      <div x-data="gamesData()" x-init="init()">
        <!-- Hiệu ứng loading -->
        <div x-show="loading" x-cloak class="container px-3 md:px-10">
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-3 2xl:gap-5">
            <template x-for="n in 9" :key="n">
             <div class="col-span-1 relative bg-gradient-to-br from-[#13112E] to-[#1a1640] border border-[#5081FF33] rounded-2xl p-4 shadow-lg animate-pulse overflow-hidden">
          <!-- Background Pattern -->
          <div class="absolute inset-0 opacity-5">
            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-[#5081FF] to-transparent transform -skew-y-1"></div>
          </div>

          <!-- Skeleton Image -->
          <div class="relative w-full h-44 bg-gradient-to-r from-[#2A2D4F] to-[#3463DB] mb-4 rounded-xl overflow-hidden">
            <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -translate-x-full animate-shimmer"></div>
          </div>

          <!-- Skeleton Title -->
          <div class="h-5 bg-gradient-to-r from-[#2A2D4F] to-[#3463DB] w-3/4 mb-3 rounded-lg"></div>

          <!-- Skeleton Stats -->
          <div class="space-y-2 mb-4">
            <div class="h-3 bg-gradient-to-r from-[#2A2D4F] to-[#3463DB] w-1/2 rounded"></div>
            <div class="h-3 bg-gradient-to-r from-[#2A2D4F] to-[#3463DB] w-2/3 rounded"></div>
          </div>

          <!-- Skeleton Button -->
          <div class="h-10 bg-gradient-to-r from-[#4B7DFF] to-[#5081FF] rounded-xl"></div>
        </div>
            </template>
          </div>
        </div>
        <!-- Danh sách tài khoản -->
        <!-- Redesigned Clean Account Display -->
        <div x-show="!loading" x-cloak>
          <!-- Optimized Account Grid Container -->
          <div class="mx-auto">
            <!-- Compact Grid Layout -->
            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-5 lg:gap-6">
              <!-- Enhanced Gaming Account Cards -->
              <template x-for="category in games" :key="category.id">
                <article x-data="{
                  isHovered: false,
                  isSelected: false,
                  topLeftColor: getRandomTriangleColor(),
                  bottomRightColor: getRandomTriangleColor(),
                  topLeftHoverColor: getRandomTriangleColor(),
                  bottomRightHoverColor: getRandomTriangleColor()
                }"
                style="background: rgb(39 36 80/var(--tw-bg-opacity,1));"
                @mouseenter="isHovered = true"
                @mouseleave="isHovered = false"
                @click="isSelected = !isSelected"
                class="clean-account-card gaming-card-hover "
                :class="isSelected ? 'clean-account-card--selected' : ''">

                  <!-- Dynamic Triangular Highlight - Top Left -->
                  <div class="triangle-top-left absolute top-0 left-0 w-12 h-12 transition-all duration-500"
                       :class="isHovered ? 'opacity-100' : 'opacity-70'"
                       :style="isHovered ?
                         `clip-path: polygon(0 0, 100% 0, 0 100%); background: linear-gradient(to bottom right, ${topLeftHoverColor}60, ${topLeftHoverColor}40);` :
                         `clip-path: polygon(0 0, 100% 0, 0 100%); background: linear-gradient(to bottom right, ${topLeftColor}40, ${topLeftColor}25);`"
                       ></div>

                  <!-- Dynamic Triangular Highlight - Bottom Right -->
                  <div class="triangle-bottom-right absolute bottom-0 right-0 w-10 h-10 transition-all duration-500"
                       :class="isHovered ? 'opacity-100' : 'opacity-60'"
                       :style="isHovered ?
                         `clip-path: polygon(100% 0, 100% 100%, 0 100%); background: linear-gradient(to top left, ${bottomRightHoverColor}50, ${bottomRightHoverColor}30);` :
                         `clip-path: polygon(100% 0, 100% 100%, 0 100%); background: linear-gradient(to top left, ${bottomRightColor}30, ${bottomRightColor}20);`"
                       ></div>

                 

                  <!-- Clean Image Section -->
                  <div class="clean-image-section">
                    <a :href="`/account/${category.slug}`" class="image-link">
                      <div class="image-container" x-data="{ loaded: false }">
                        <!-- Clean Loading State -->
                        <div x-show="!loaded" x-cloak class="image-skeleton">
                          <div class="skeleton-shimmer"></div>
                        </div>
                        <img @load="loaded = true"
                             :src="category.images"
                             :alt="category.name"
                            
                             class="account-image">
                        <!-- Hover Overlay -->
                        <div class="image-overlay" :class="isHovered ? 'opacity-100' : 'opacity-0'">
                          <i class="fas fa-eye"></i>
                        </div>
                      </div>
                    </a>

                    <!-- Account Code Badge -->
                    <div class="account-code-badge">
                      <span x-text="category.code"></span>
                    </div>
                  </div>
                  <!-- Clean Account Title with Truncation -->
                  <div class="clean-account-title">
                    <h3 x-text="category.name"
                        :title="category.name"
                        class="account-title-truncated"></h3>
                  </div>

                  <!-- Clean Account Stats Grid -->
                  <div class="clean-stats-grid">
                    <template x-if="category.game_server && category.game_server !== 'null' && category.server && category.server !== 'null'">
                      <div class="stat-item">
                        <span class="stat-label" x-text="category.game_server"></span>
                        <span class="stat-value" x-text="category.server"></span>
                      </div>
                    </template>

                    <template x-if="category.game_rank && category.game_rank !== 'null' && category.rank && category.rank !== 'null'">
                      <div class="stat-item">
                        <span class="stat-label" x-text="category.game_rank"></span>
                        <span class="stat-value" x-text="category.rank"></span>
                      </div>
                    </template>

                    <template x-if="category.game_level && category.game_level !== 'null' && category.level && category.level !== 'null'">
                      <div class="stat-item">
                        <span class="stat-label" x-text="category.game_level"></span>
                        <span class="stat-value" x-text="category.level"></span>
                      </div>
                    </template>

                    <template x-if="category.game_attribute_4 && category.game_attribute_4 !== 'null' && category.attribute_4 && category.attribute_4 !== 'null'">
                      <div class="stat-item">
                        <span class="stat-label" x-text="category.game_attribute_4"></span>
                        <span class="stat-value" x-text="category.attribute_4"></span>
                      </div>
                    </template>

                    <template x-if="category.game_attribute_5 && category.game_attribute_5 !== 'null' && category.attribute_5 && category.attribute_5 !== 'null'">
                      <div class="stat-item">
                        <span class="stat-label" x-text="category.game_attribute_5"></span>
                        <span class="stat-value" x-text="category.attribute_5"></span>
                      </div>
                    </template>
                  </div>


                  <!-- Clean Character Section -->
                  <template x-if="category.game_character && category.game_character !== 'null' && category.char.count > 0">
                    <div class="clean-feature-section">
                      <div class="feature-header">
                        <h4 x-text="category.game_character"></h4>
                        <div class="feature-dropdown" x-data="dropdown" @click.outside="open = false">
                          <button class="feature-toggle-btn" @click="toggle">
                            <span>Xem chi tiết</span>
                            <i class="fas fa-chevron-down"></i>
                          </button>

                          <div x-show="open" class="feature-dropdown-content">
                            <div class="dropdown-header">
                              <span class="count-label" x-text="'Count: ' + category.char.count"></span>
                              <button type="button" class="close-btn" @click="toggle">
                                <i class="fas fa-times"></i>
                              </button>
                            </div>

                            <div class="feature-grid">
                              <template x-for="(char, charIndex) in category.char.data" :key="charIndex">
                                <div class="feature-item"
                                     :data-tippy-content="char.name"
                                     data-theme="primary"
                                     x-init="tippy($el, { theme: 'primary', animation: 'perspective', placement: 'top' })"
                                     style="background-image: url('/assets/image/d7bb309392a99e4179abbf83cba73c3d.png'); background-size: cover; background-position: center;">
                                  <img :src="char.img" :alt="char.name" class="feature-image">
                                </div>
                              </template>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Clean Marquee Display -->
                      <div class="clean-marquee-container" x-data="marqueeData(category.char.data)" x-init="init()">
                        <div class="marquee-wrapper">
                          <div x-ref="track" class="marquee-track" :style="'animation-duration: ' + duration + 's'">
                            <div x-ref="copy" class="marquee-content">
                              <template x-for="(item, index) in images" :key="index">
                                <div class="marquee-item"
                                     :data-tippy-content="item.name"
                                     data-theme="primary"
                                     x-init="tippy($el, { theme: 'primary', animation: 'perspective', placement: 'top' })"
                                     style="background-image: url('/assets/image/d7bb309392a99e4179abbf83cba73c3d.png'); background-size: cover; background-position: center;">
                                  <img :src="item.img" :alt="item.name" class="marquee-image" />
                                </div>
                              </template>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </template>
                  <!-- Clean Weapon Section -->
                  <template x-if="category.game_weapon && category.game_weapon !== 'null' && category.weapon.count > 0">
                    <div class="clean-feature-section">
                      <div class="feature-header">
                        <h4 x-text="category.game_weapon"></h4>
                        <div class="feature-dropdown" x-data="dropdown" @click.outside="open = false">
                          <button class="feature-toggle-btn" @click="toggle">
                            <span>Xem chi tiết</span>
                            <i class="fas fa-chevron-down"></i>
                          </button>

                          <div x-show="open" class="feature-dropdown-content">
                            <div class="dropdown-header">
                              <span class="count-label" x-text="'Count: ' + category.weapon.count"></span>
                              <button type="button" class="close-btn" @click="toggle">
                                <i class="fas fa-times"></i>
                              </button>
                            </div>

                            <div class="feature-grid">
                              <template x-for="(weapon, weaponIndex) in category.weapon.data" :key="weaponIndex">
                                <div class="feature-item"
                                     :data-tippy-content="weapon.name"
                                     data-theme="primary"
                                     x-init="tippy($el, { theme: 'primary', animation: 'perspective', placement: 'top' })"
                                     style="background-image: url('/assets/image/d7bb309392a99e4179abbf83cba73c3d.png'); background-size: cover; background-position: center;">
                                  <img :src="weapon.img" :alt="weapon.name" class="feature-image">
                                </div>
                              </template>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Clean Marquee Display -->
                      <div class="clean-marquee-container" x-data="marqueeData(category.weapon.data)" x-init="init()">
                        <div class="marquee-wrapper">
                          <div x-ref="track" class="marquee-track" :style="'animation-duration: ' + duration + 's'">
                            <div x-ref="copy" class="marquee-content">
                              <template x-for="(item, index) in images" :key="index">
                                <div class="marquee-item"
                                     :data-tippy-content="item.name"
                                     data-theme="primary"
                                     x-init="tippy($el, { theme: 'primary', animation: 'perspective', placement: 'top' })"
                                     style="background-image: url('/assets/image/d7bb309392a99e4179abbf83cba73c3d.png'); background-size: cover; background-position: center;">
                                  <img :src="item.img" :alt="item.name" class="marquee-image" />
                                </div>
                              </template>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </template>
                  <!-- Clean Skin Section -->
                  <template x-if="category.game_skin && category.game_skin !== 'null' && category.skin.count > 0">
                    <div class="clean-feature-section">
                      <div class="feature-header">
                        <h4 x-text="category.game_skin"></h4>
                        <div class="feature-dropdown" x-data="dropdown" @click.outside="open = false">
                          <button class="feature-toggle-btn" @click="toggle">
                            <span>Xem chi tiết</span>
                            <i class="fas fa-chevron-down"></i>
                          </button>

                          <div x-show="open" class="feature-dropdown-content">
                            <div class="dropdown-header">
                              <span class="count-label" x-text="'Count: ' + category.skin.count"></span>
                              <button type="button" class="close-btn" @click="toggle">
                                <i class="fas fa-times"></i>
                              </button>
                            </div>

                            <div class="feature-grid">
                              <template x-for="(skin, skinIndex) in category.skin.data" :key="skinIndex">
                                <div class="feature-item"
                                     :data-tippy-content="skin.name"
                                     data-theme="primary"
                                     x-init="tippy($el, { theme: 'primary', animation: 'perspective', placement: 'top' })"
                                     style="background-image: url('/assets/image/d7bb309392a99e4179abbf83cba73c3d.png'); background-size: cover; background-position: center;">
                                  <img :src="skin.img" :alt="skin.name" class="feature-image">
                                </div>
                              </template>
                            </div>
                          </div>
                        </div>
                      </div>

                      <!-- Clean Marquee Display -->
                      <div class="clean-marquee-container" x-data="marqueeData(category.skin.data)" x-init="init()">
                        <div class="marquee-wrapper">
                          <div x-ref="track" class="marquee-track" :style="'animation-duration: ' + duration + 's'">
                            <div x-ref="copy" class="marquee-content">
                              <template x-for="(item, index) in images" :key="index">
                                <div class="marquee-item"
                                     :data-tippy-content="item.name"
                                     data-theme="primary"
                                     x-init="tippy($el, { theme: 'primary', animation: 'perspective', placement: 'top' })"
                                     style="background-image: url('/assets/image/d7bb309392a99e4179abbf83cba73c3d.png'); background-size: cover; background-position: center;">
                                  <img :src="item.img" :alt="item.name" class="marquee-image" />
                                </div>
                              </template>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </template>

<style>
  /* Enhanced Gaming Account Card Styles - Matching game.php */
  .clean-account-card {
    background: linear-gradient(145deg, #272450 0%, #1e293b 50%, #272450 100%);
    border: 2px solid transparent;
    border-radius: 0.75rem;
    padding: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    display: flex;
    flex-direction: column;
    min-height: 360px;
    position: relative;
    overflow: hidden;
    transform-origin: center;
    will-change: transform, box-shadow, border-color;
  }

  .clean-account-card:hover {
    transform: scale(1.05) translateY(-8px);
    border-color: #5081FF !important;
    box-shadow:
      0 20px 40px rgba(80, 129, 255, 0.4),
      0 0 30px rgba(80, 129, 255, 0.3),
      0 0 60px rgba(75, 125, 255, 0.2);
    animation: gaming-card-glow 2s infinite;
  }

  .clean-account-card--selected {
    border-color: #5081FF;
    box-shadow: 0 0 0 1px rgba(80, 129, 255, 0.3);
  }

  /* Compact Card Header */
  .clean-card-header {
    display: flex;
    justify-content: flex-end;
    margin-bottom: 0.75rem;
  }

  .status-badge {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    background: rgba(16, 185, 129, 0.1);
    border: 1px solid rgba(16, 185, 129, 0.3);
    border-radius: 0.375rem;
    padding: 0.125rem 0.5rem;
    font-size: 0.6875rem;
    font-weight: 500;
    color: #10b981;
  }

  .status-indicator {
    width: 4px;
    height: 4px;
    background: #10b981;
    border-radius: 50%;
    animation: pulse 2s infinite;
  }

  /* Optimized Image Section for 850x500 aspect ratio */
  .clean-image-section {
    position: relative;
    margin-bottom: 1rem;
  }

  .image-container {
    position: relative;
    width: 100%;
    height: 0;
    padding-bottom: 58.82%; /* 500/850 = 0.5882 for 850x500 aspect ratio */
    border-radius: 0.5rem;
    overflow: hidden;
    background: rgba(30, 41, 59, 0.5);
  }

  .account-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: center;
    transition: transform 0.3s ease;
  }

  .clean-account-card:hover .account-image {
    transform: scale(1.03);
  }

  .image-overlay {
    position: absolute;
    inset: 0;
    background: rgba(0, 0, 0, 0.4);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: opacity 0.3s ease;
    color: white;
    font-size: 1.25rem;
  }

  .image-skeleton {
    position: absolute;
    inset: 0;
    background: linear-gradient(90deg, #374151 25%, #4b5563 50%, #374151 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }

  .account-code-badge {
    position: absolute;
    bottom: -6px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #5081FF, #3463DB);
    color: white;
    padding: 0.125rem 0.75rem;
    border-radius: 0.375rem;
    font-size: 0.6875rem;
    font-weight: 600;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
    z-index: 10;
  }

  /* Enhanced Mobile-First Account Title Styling */
  .clean-account-title {
    margin-bottom: 0.75rem;
    text-align: center;
    padding: 0 0.5rem;
    min-height: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .clean-account-title h3 {
    font-size: 1rem;
    font-weight: 600;
    color: #ffffff;
    margin: 0;
    font-family: 'Signika', sans-serif;
    line-height: 1.3;
    text-align: center;
  }

  /* Improved Account Title Truncation - Mobile First */
  .account-title-truncated {
    display: block;
    width: 100%;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    cursor: help;
    transition: all 0.3s ease;
    padding: 0.25rem 0;
    border-radius: 0.375rem;
    background: rgba(255, 255, 255, 0.02);
    backdrop-filter: blur(4px);
  }

  .account-title-truncated:hover {
    color: #5081FF;
    transform: scale(1.02);
    background: rgba(80, 129, 255, 0.1);
    box-shadow: 0 0 10px rgba(80, 129, 255, 0.2);
  }

  /* Simple Stats Grid */
  .clean-stats-grid {
    margin-bottom: 1rem;
  }

  .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .stat-item:last-child {
    border-bottom: none;
  }

  .stat-label {
    font-size: 0.875rem;
    color: #9ca3af;
    font-weight: 400;
    line-height: 1.5;
  }

  .stat-value {
    font-size: 0.875rem;
    font-weight: 500;
    color: #ffffff;
    text-align: right;
    line-height: 1.5;
  }

  /* Compact Feature Sections */
  .clean-feature-section {
    margin-bottom: 0.75rem;
  }

  .feature-header {
    margin-bottom: 0.5rem;
  }

  .feature-header h4 {
    font-size: 0.8125rem;
    font-weight: 600;
    color: #5081FF;
    margin: 0 0 0.375rem 0;
    text-align: center;
    line-height: 1.2;
  }

  .feature-dropdown {
    position: relative;
  }

  .feature-toggle-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.375rem;
    width: 100%;
    padding: 0.375rem 0.5rem;
    background: rgba(80, 129, 255, 0.1);
    border: 1px solid rgba(80, 129, 255, 0.3);
    border-radius: 0.375rem;
    color: #ffffff;
    font-size: 0.6875rem;
    cursor: pointer;
    transition: all 0.3s ease;
  }

  .feature-toggle-btn:hover {
    background: rgba(80, 129, 255, 0.2);
    border-color: rgba(80, 129, 255, 0.5);
  }

  .feature-dropdown-content {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    z-index: 50;
    background: #1e293b;
    border: 1px solid rgba(80, 129, 255, 0.3);
    border-radius: 0.5rem;
    margin-top: 0.25rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  }

  .dropdown-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  }

  .count-label {
    font-size: 0.75rem;
    color: #9ca3af;
  }

  .close-btn {
    background: none;
    border: none;
    color: #9ca3af;
    cursor: pointer;
    padding: 0.25rem;
    border-radius: 0.25rem;
    transition: color 0.3s ease;
  }

  .close-btn:hover {
    color: #ffffff;
  }

  .feature-grid {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 0.75rem;
    padding: 1rem;
    max-height: 160px;
    overflow-y: auto;
  }

  .feature-item {
    width: 40px;
    height: 40px;
    border-radius: 0.5rem;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: transform 0.3s ease;
    cursor: pointer;
  }

  .feature-item:hover {
    transform: scale(1.1);
  }

  .feature-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  /* Marquee */
  .clean-marquee-container {
    margin-top: 0.75rem;
  }

  .marquee-wrapper {
    position: relative;
    width: 100%;
    overflow: hidden;
    border-radius: 0.5rem;
    background: rgba(255, 255, 255, 0.05);
    padding: 0.5rem;
  }

  .marquee-content {
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .marquee-item {
    flex-shrink: 0;
    width: 32px;
    height: 32px;
    border-radius: 0.375rem;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .marquee-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  /* Compact Purchase Section */
  .clean-purchase-section {
    margin-top: auto;
    padding-top: 0.75rem;
  }

  .clean-purchase-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    width: 100%;
    padding: 0.625rem 0.75rem;
    background: linear-gradient(135deg, #5081FF, #3463DB);
    border: none;
    border-radius: 0.5rem;
    color: white;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px -1px rgba(80, 129, 255, 0.3);
  }

  .clean-purchase-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(80, 129, 255, 0.4);
    background: linear-gradient(135deg, #3463DB, #1e40af);
  }

  .purchase-icon {
    font-size: 0.875rem;
  }

  .purchase-price {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.125rem;
  }

  .price-with-discount {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 0.0625rem;
  }

  .original-price {
    font-size: 0.6875rem;
    text-decoration: line-through;
    color: rgba(255, 255, 255, 0.6);
    line-height: 1;
  }

  .discounted-price,
  .regular-price {
    font-size: 0.8125rem;
    font-weight: 700;
    color: #ffffff;
    line-height: 1.2;
  }

  /* Animations */
  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }

  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }

  @keyframes marquee {
    from { transform: translateX(0); }
    to { transform: translateX(calc(-1 * var(--move-distance))); }
  }

  .marquee-track {
    display: flex;
    white-space: nowrap;
    animation: marquee linear infinite;
  }

  .marquee-track:hover {
    animation-play-state: paused;
  }

  /* Responsive Design */
  @media (max-width: 640px) {
    .clean-account-card {
      padding: 0.75rem;
      min-height: 320px;
    }

    .clean-card-header {
      margin-bottom: 0.5rem;
    }

    .clean-image-section {
      margin-bottom: 0.75rem;
    }

    .clean-account-title {
      margin-bottom: 0.5rem;
    }

    .clean-account-title h3 {
      font-size: 0.9375rem;
    }

    /* Mobile: Improved readability with balanced truncation */
    .clean-account-title {
      margin-bottom: 0.625rem;
      padding: 0 0.25rem;
      min-height: 3rem;
    }

    .clean-account-title h3 {
      font-size: 0.95rem;
      line-height: 1.4;
      font-weight: 600;
    }

    .account-title-truncated {
      max-width: calc(100% - 1rem);
      margin: 0 auto;
      padding: 0.375rem 0.5rem;
      font-size: 0.95rem;
      background: rgba(255, 255, 255, 0.05);
      border: 1px solid rgba(80, 129, 255, 0.2);
    }

    .account-title-truncated:hover {
      background: rgba(80, 129, 255, 0.15);
      border-color: rgba(80, 129, 255, 0.4);
      transform: scale(1.01);
    }



    .feature-header h4 {
      font-size: 0.75rem;
    }

    .feature-toggle-btn {
      padding: 0.25rem 0.375rem;
      font-size: 0.625rem;
    }

    .feature-grid {
      gap: 0.375rem;
      padding: 0.5rem;
      max-height: 120px;
    }

    .feature-item {
      width: 32px;
      height: 32px;
    }

    .marquee-item {
      width: 24px;
      height: 24px;
    }

    .clean-purchase-btn {
      padding: 0.5rem 0.625rem;
    }

    .purchase-icon {
      font-size: 0.75rem;
    }

    .original-price {
      font-size: 0.625rem;
    }

    .discounted-price,
    .regular-price {
      font-size: 0.75rem;
    }
  }

  @media (max-width: 480px) {
    .clean-account-card {
      padding: 0.625rem;
      min-height: 300px;
    }

    .clean-stats-grid {
      padding: 0.375rem;
    }

    .feature-grid {
      padding: 0.375rem;
    }

    /* Extra Small: Maintain readability while being compact */
    .clean-account-title {
      min-height: 2.75rem;
      padding: 0 0.125rem;
    }

    .clean-account-title h3 {
      font-size: 0.9rem;
      line-height: 1.35;
    }

    .account-title-truncated {
      max-width: calc(100% - 0.5rem);
      font-size: 0.9rem;
      padding: 0.3rem 0.4rem;
    }
  }

  /* Tablet responsive design */
  @media (min-width: 641px) and (max-width: 1024px) {
    .account-title-truncated {
      max-width: 220px;
    }
  }

  /* Desktop responsive design */
  @media (min-width: 1025px) {
    .account-title-truncated {
      max-width: 280px;
    }
  }

  /* Large desktop responsive design */
  @media (min-width: 1440px) {
    .account-title-truncated {
      max-width: 320px;
    }
  }

  /* Gaming Card Animations and Effects */
  @keyframes gaming-card-glow {
    0%, 100% {
      box-shadow:
        0 20px 40px rgba(80, 129, 255, 0.3),
        0 0 20px rgba(80, 129, 255, 0.2),
        0 0 40px rgba(75, 125, 255, 0.1);
    }
    50% {
      box-shadow:
        0 25px 50px rgba(80, 129, 255, 0.5),
        0 0 30px rgba(80, 129, 255, 0.4),
        0 0 60px rgba(75, 125, 255, 0.3);
    }
  }

  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }

  .animate-shimmer {
    animation: shimmer 2s infinite;
  }

  /* Responsive Gaming Card Effects */
  @media (max-width: 768px) {
    .clean-account-card:hover {
      transform: scale(1.02) translateY(-4px);
    }
  }

  @media (max-width: 640px) {
    .clean-account-card:hover {
      transform: scale(1.01) translateY(-2px);
    }
  }

  /* Touch Device Optimizations */
  @media (hover: none) and (pointer: coarse) {
    .clean-account-card:active {
      transform: scale(1.02) translateY(-2px);
      transition: all 0.15s ease;
    }
  }

  /* High Performance Mode for Lower-End Devices */
  @media (prefers-reduced-motion: reduce) {
    .clean-account-card {
      transition: transform 0.2s ease, border-color 0.2s ease;
    }

    .clean-account-card:hover {
      transform: scale(1.02) translateY(-2px);
      animation: none;
    }
  }

  /* ===== GAMING PAGINATION STYLES ===== */
  .gaming-pagination-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background: linear-gradient(145deg, rgba(19, 17, 46, 0.8) 0%, rgba(30, 41, 59, 0.6) 50%, rgba(19, 17, 46, 0.8) 100%);
    border: 1px solid rgba(80, 129, 255, 0.2);
    border-radius: 1rem;
    backdrop-filter: blur(10px);
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      0 0 20px rgba(80, 129, 255, 0.1);
  }

  .gaming-pagination-list {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    list-style: none;
    margin: 0;
    padding: 0;
  }

  .gaming-pagination-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    min-width: 2.5rem;
    height: 2.5rem;
    padding: 0.5rem 0.75rem;
    border: none;
    border-radius: 0.5rem;
    font-family: 'Montserrat', sans-serif;
    font-size: 0.875rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    position: relative;
    overflow: hidden;
  }

  .gaming-pagination-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
  }

  .gaming-pagination-btn:hover::before {
    left: 100%;
  }

  /* Number Buttons */
  .gaming-pagination-btn--number {
    background: linear-gradient(145deg, rgba(39, 36, 80, 0.8) 0%, rgba(30, 41, 59, 0.6) 100%);
    color: #FFFFFF99;
    border: 1px solid rgba(80, 129, 255, 0.3);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
  }

  .gaming-pagination-btn--number:hover {
    background: linear-gradient(145deg, rgba(80, 129, 255, 0.2) 0%, rgba(75, 125, 255, 0.3) 100%);
    color: #FFFFFF;
    border-color: rgba(80, 129, 255, 0.6);
    transform: translateY(-2px) scale(1.05);
    box-shadow:
      0 8px 16px rgba(80, 129, 255, 0.3),
      0 0 20px rgba(80, 129, 255, 0.2);
  }

  /* Active Page Button */
  .gaming-pagination-btn--active {
    background: linear-gradient(135deg, #4B7DFF 0%, #5081FF 50%, #3463DB 100%);
    color: #FFFFFF;
    border: 1px solid rgba(80, 129, 255, 0.8);
    box-shadow:
      0 4px 12px rgba(80, 129, 255, 0.4),
      0 0 20px rgba(80, 129, 255, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    animation: gaming-pagination-pulse 2s infinite;
  }

  .gaming-pagination-btn--active:hover {
    transform: translateY(-1px) scale(1.02);
    box-shadow:
      0 6px 16px rgba(80, 129, 255, 0.5),
      0 0 30px rgba(80, 129, 255, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.3);
  }

  /* Navigation Buttons (Previous/Next) */
  .gaming-pagination-btn--nav {
    background: linear-gradient(145deg, rgba(75, 125, 255, 0.1) 0%, rgba(80, 129, 255, 0.2) 100%);
    color: #5081FF;
    border: 1px solid rgba(80, 129, 255, 0.4);
    min-width: 3rem;
  }

  .gaming-pagination-btn--nav:hover {
    background: linear-gradient(145deg, rgba(75, 125, 255, 0.3) 0%, rgba(80, 129, 255, 0.4) 100%);
    color: #FFFFFF;
    border-color: rgba(80, 129, 255, 0.8);
    transform: translateY(-2px) scale(1.05);
    box-shadow:
      0 8px 16px rgba(80, 129, 255, 0.3),
      0 0 20px rgba(80, 129, 255, 0.2);
  }

  /* Disabled Buttons */
  .gaming-pagination-btn--disabled {
    background: linear-gradient(145deg, rgba(39, 36, 80, 0.3) 0%, rgba(30, 41, 59, 0.2) 100%);
    color: #FFFFFF33;
    border: 1px solid rgba(80, 129, 255, 0.1);
    cursor: not-allowed;
    opacity: 0.5;
  }

  .gaming-pagination-btn--disabled:hover {
    transform: none;
    box-shadow: none;
    background: linear-gradient(145deg, rgba(39, 36, 80, 0.3) 0%, rgba(30, 41, 59, 0.2) 100%);
  }

  .gaming-pagination-btn--disabled::before {
    display: none;
  }

  /* Ellipsis */
  .gaming-pagination-ellipsis {
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 2.5rem;
    height: 2.5rem;
    color: #FFFFFF66;
    font-size: 1rem;
  }

  /* Icons */
  .gaming-pagination-icon {
    flex-shrink: 0;
    transition: transform 0.3s ease;
  }

  .gaming-pagination-btn:hover .gaming-pagination-icon {
    transform: scale(1.1);
  }

  .gaming-pagination-text {
    font-size: 0.875rem;
    font-weight: 600;
  }

  /* Pagination Info */
  .gaming-pagination-info {
    text-align: center;
    margin-top: 0.5rem;
  }

  .gaming-pagination-info-text {
    font-size: 0.875rem;
    color: #FFFFFF99;
    font-family: 'Montserrat', sans-serif;
  }

  .gaming-pagination-info-current,
  .gaming-pagination-info-total {
    font-weight: 700;
    color: #5081FF;
    text-shadow: 0 0 8px rgba(80, 129, 255, 0.5);
  }

  /* Gaming Pagination Animations */
  @keyframes gaming-pagination-pulse {
    0%, 100% {
      box-shadow:
        0 4px 12px rgba(80, 129, 255, 0.4),
        0 0 20px rgba(80, 129, 255, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    }
    50% {
      box-shadow:
        0 6px 16px rgba(80, 129, 255, 0.6),
        0 0 30px rgba(80, 129, 255, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    }
  }

  @keyframes gaming-pagination-glow {
    0%, 100% {
      box-shadow: 0 0 20px rgba(80, 129, 255, 0.2);
    }
    50% {
      box-shadow: 0 0 30px rgba(80, 129, 255, 0.4);
    }
  }

  /* Responsive Design for Gaming Pagination */
  @media (max-width: 768px) {
    .gaming-pagination-container {
      padding: 1.25rem 1rem;
      gap: 1rem;
      margin: 0 0.5rem;
    }

    .gaming-pagination-list {
      gap: 0.5rem;
      flex-wrap: wrap;
      justify-content: center;
      align-items: center;
    }

    .gaming-pagination-btn {
      min-width: 2.75rem;
      height: 2.75rem;
      padding: 0.5rem 0.75rem;
      font-size: 0.875rem;
      border-radius: 0.75rem;
    }

    .gaming-pagination-btn--nav {
      min-width: 3.25rem;
      padding: 0.5rem 0.875rem;
    }

    .gaming-pagination-text {
      font-size: 0.8125rem;
      font-weight: 600;
    }

    .gaming-pagination-info {
      margin-top: 0.75rem;
    }

    .gaming-pagination-info-text {
      font-size: 0.9375rem;
    }

    .gaming-pagination-ellipsis {
      min-width: 2.75rem;
      height: 2.75rem;
      margin: 0 0.25rem;
    }
  }

  /* Small Mobile Optimization */
  @media (max-width: 640px) {
    .gaming-pagination-container {
      padding: 1rem 0.75rem;
      gap: 0.875rem;
      margin: 0 0.25rem;
    }

    .gaming-pagination-list {
      gap: 0.375rem;
      max-width: 100%;
      overflow-x: auto;
      padding: 0.25rem 0;
      scrollbar-width: none;
      -ms-overflow-style: none;
    }

    .gaming-pagination-list::-webkit-scrollbar {
      display: none;
    }

    .gaming-pagination-btn {
      min-width: 2.5rem;
      height: 2.5rem;
      padding: 0.375rem 0.625rem;
      font-size: 0.8125rem;
      flex-shrink: 0;
    }

    .gaming-pagination-btn--nav {
      min-width: 2.875rem;
      padding: 0.375rem 0.75rem;
    }

    .gaming-pagination-text {
      font-size: 0.75rem;
    }

    .gaming-pagination-ellipsis {
      min-width: 2.5rem;
      height: 2.5rem;
    }
  }

  /* Extra Small Mobile */
  @media (max-width: 480px) {
    .gaming-pagination-container {
      padding: 0.875rem 0.5rem;
      gap: 0.75rem;
      margin: 0;
      border-radius: 0.75rem;
    }

    .gaming-pagination-list {
      gap: 0.25rem;
      justify-content: flex-start;
      padding: 0.125rem 0;
    }

    .gaming-pagination-text {
      display: none !important;
    }

    .gaming-pagination-btn {
      min-width: 2.25rem;
      height: 2.25rem;
      padding: 0.25rem;
      font-size: 0.75rem;
      border-radius: 0.5rem;
      flex-shrink: 0;
    }

    .gaming-pagination-btn--nav {
      min-width: 2.5rem;
      padding: 0.25rem 0.375rem;
    }

    .gaming-pagination-ellipsis {
      min-width: 2.25rem;
      height: 2.25rem;
      font-size: 0.875rem;
    }

    .gaming-pagination-icon {
      width: 16px;
      height: 16px;
    }

    .gaming-pagination-info-text {
      font-size: 0.8125rem;
    }
  }

  /* Ultra Small Mobile */
  @media (max-width: 360px) {
    .gaming-pagination-container {
      padding: 0.75rem 0.375rem;
      gap: 0.625rem;
    }

    .gaming-pagination-list {
      gap: 0.125rem;
      padding: 0;
    }

    .gaming-pagination-btn {
      min-width: 2rem;
      height: 2rem;
      padding: 0.125rem;
      font-size: 0.6875rem;
    }

    .gaming-pagination-btn--nav {
      min-width: 2.25rem;
      padding: 0.125rem 0.25rem;
    }

    .gaming-pagination-ellipsis {
      min-width: 2rem;
      height: 2rem;
      font-size: 0.75rem;
    }

    .gaming-pagination-icon {
      width: 14px;
      height: 14px;
    }

    .gaming-pagination-info-text {
      font-size: 0.75rem;
    }
  }

  /* Mobile Touch Optimizations */
  @media (max-width: 640px) {
    .gaming-pagination-btn:hover {
      transform: translateY(-1px) scale(1.02);
    }

    .gaming-pagination-btn:active {
      transform: translateY(0) scale(0.98);
      transition: all 0.1s ease;
    }

    /* Ensure proper spacing on mobile */
    .gaming-pagination-list li {
      display: flex;
      align-items: center;
      justify-content: center;
    }

    /* Better visual balance */
    .gaming-pagination-container {
      box-shadow:
        0 4px 16px rgba(0, 0, 0, 0.2),
        0 0 12px rgba(80, 129, 255, 0.1);
    }
  }

  /* Tablet specific adjustments */
  @media (min-width: 641px) and (max-width: 1023px) {
    .gaming-pagination-container {
      padding: 1.5rem 1.25rem;
      gap: 1.125rem;
    }

    .gaming-pagination-list {
      gap: 0.625rem;
    }

    .gaming-pagination-btn {
      min-width: 3rem;
      height: 3rem;
      padding: 0.625rem 0.875rem;
      font-size: 0.9375rem;
    }

    .gaming-pagination-btn--nav {
      min-width: 3.5rem;
      padding: 0.625rem 1rem;
    }
  }

  /* Large Screen Enhancements */
  @media (min-width: 1024px) {
    .gaming-pagination-container {
      padding: 2rem;
      gap: 1.25rem;
    }

    .gaming-pagination-list {
      gap: 0.75rem;
    }

    .gaming-pagination-btn {
      min-width: 3rem;
      height: 3rem;
      padding: 0.75rem 1rem;
      font-size: 1rem;
    }

    .gaming-pagination-btn--nav {
      min-width: 3.5rem;
    }

    .gaming-pagination-text {
      font-size: 1rem;
    }

    .gaming-pagination-info-text {
      font-size: 1rem;
    }
  }

  /* High-end Gaming Effects for Desktop */
  @media (min-width: 1024px) and (hover: hover) {
    .gaming-pagination-container {
      animation: gaming-pagination-glow 3s infinite;
    }

    .gaming-pagination-btn--number:hover {
      animation: gaming-pagination-pulse 1s infinite;
    }

    .gaming-pagination-btn--nav:hover {
      background: linear-gradient(145deg, rgba(75, 125, 255, 0.4) 0%, rgba(80, 129, 255, 0.6) 100%);
      box-shadow:
        0 12px 24px rgba(80, 129, 255, 0.4),
        0 0 30px rgba(80, 129, 255, 0.3);
    }
  }

  /* Accessibility and Performance */
  @media (prefers-reduced-motion: reduce) {
    .gaming-pagination-btn {
      transition: background-color 0.2s ease, color 0.2s ease;
    }

    .gaming-pagination-btn:hover {
      transform: none;
      animation: none;
    }

    .gaming-pagination-btn--active {
      animation: none;
    }

    .gaming-pagination-container {
      animation: none;
    }

    .gaming-pagination-btn::before {
      display: none;
    }
  }

  /* Dark Mode Optimizations */
  @media (prefers-color-scheme: dark) {
    .gaming-pagination-container {
      background: linear-gradient(145deg, rgba(13, 11, 36, 0.9) 0%, rgba(20, 25, 45, 0.8) 50%, rgba(13, 11, 36, 0.9) 100%);
      border-color: rgba(80, 129, 255, 0.3);
    }

    .gaming-pagination-btn--number {
      background: linear-gradient(145deg, rgba(25, 22, 60, 0.9) 0%, rgba(20, 25, 45, 0.8) 100%);
      border-color: rgba(80, 129, 255, 0.4);
    }
  }

  /* Focus States for Accessibility */
  .gaming-pagination-btn:focus {
    outline: none;
    box-shadow:
      0 0 0 2px rgba(80, 129, 255, 0.5),
      0 4px 12px rgba(80, 129, 255, 0.3);
  }

  .gaming-pagination-btn:focus-visible {
    outline: 2px solid #5081FF;
    outline-offset: 2px;
  }

  /* Mobile Layout Utilities */
  .gaming-pagination-mobile-scroll {
    overflow-x: auto;
    overflow-y: hidden;
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
  }

  .gaming-pagination-mobile-scroll::-webkit-scrollbar {
    height: 2px;
  }

  .gaming-pagination-mobile-scroll::-webkit-scrollbar-track {
    background: rgba(80, 129, 255, 0.1);
    border-radius: 1px;
  }

  .gaming-pagination-mobile-scroll::-webkit-scrollbar-thumb {
    background: rgba(80, 129, 255, 0.3);
    border-radius: 1px;
  }

  .gaming-pagination-mobile-scroll::-webkit-scrollbar-thumb:hover {
    background: rgba(80, 129, 255, 0.5);
  }

  /* Prevent layout shift on mobile */
  .gaming-pagination-list {
    min-height: 2.5rem;
  }

  @media (max-width: 768px) {
    .gaming-pagination-list {
      min-height: 2.75rem;
    }
  }

  @media (max-width: 480px) {
    .gaming-pagination-list {
      min-height: 2.25rem;
    }
  }

  /* Ensure consistent button alignment */
  .gaming-pagination-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    vertical-align: middle;
    box-sizing: border-box;
  }

  /* Prevent text selection on mobile */
  .gaming-pagination-container {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
  }
</style>


                  <!-- Clean Purchase Section -->
                  <div class="clean-purchase-section">
                    <a :href="`/account/${category.slug}`" class="clean-purchase-btn">
                      <div class="purchase-icon">
                        <i class="fas fa-shopping-cart"></i>
                      </div>
                      <div class="purchase-price">
                        <template x-if="parseFloat(category.discount) > 0">
                          <div class="price-with-discount">
                            <span class="original-price" x-text="formatCash(category.money) + ' ' + category.cash"></span>
                            <span class="discounted-price" x-text="formatCash(category.money - category.money * (parseFloat(category.discount) / 100)) + ' ' + category.cash"></span>
                          </div>
                        </template>
                        <template x-if="category.discount == 0">
                          <span class="regular-price" x-text="formatCash(category.money) + ' ' + category.cash"></span>
                        </template>
                      </div>
                    </a>
                  </div>
                </article>
              </template>
            </div>
          </div>
        </div>

        <!-- Debug Information (Remove in production) -->
        <!-- <div class="mt-4 p-4 bg-gray-800 text-white text-sm rounded" x-show="true">
          <p>Debug Info:</p>
          <p>Total Pages: <span x-text="totalPages"></span></p>
          <p>Current Page: <span x-text="currentPage"></span></p>
          <p>Pages Array: <span x-text="JSON.stringify(pages)"></span></p>
          <p>Games Count: <span x-text="games.length"></span></p>
        </div> -->

        <!-- Gaming-Themed Pagination Section -->
        <template x-if="totalPages > 0">
          <div class="flex w-full flex-col justify-center mt-8 mb-6">
            <!-- Pagination Container with Gaming Theme -->
            <div class="mx-auto">
              <ul class="gaming-pagination-list gaming-pagination-mobile-scroll">
                <!-- Previous Button -->
                <li>
                  <button @click="goToPage(currentPage - 1)"
                          :disabled="currentPage === 1"
                          :class="currentPage === 1 ? 'gaming-pagination-btn gaming-pagination-btn--disabled' : 'gaming-pagination-btn gaming-pagination-btn--nav'"
                          class="gaming-pagination-prev">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="gaming-pagination-icon">
                      <path d="M15 5L9 12L15 19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg>
                    <span class="gaming-pagination-text hidden sm:inline">Trước</span>
                  </button>
                </li>

                <!-- Page Number Buttons -->
                <template x-for="(page, index) in pages" :key="index">
                  <li>
                    <template x-if="page === '...'">
                      <span class="gaming-pagination-ellipsis">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                          <circle cx="5" cy="12" r="2" fill="currentColor"></circle>
                          <circle cx="12" cy="12" r="2" fill="currentColor"></circle>
                          <circle cx="19" cy="12" r="2" fill="currentColor"></circle>
                        </svg>
                      </span>
                    </template>
                    <template x-if="page !== '...'">
                      <button @click="goToPage(page)"
                              x-text="page"
                              :class="page === currentPage ? 'gaming-pagination-btn gaming-pagination-btn--active' : 'gaming-pagination-btn gaming-pagination-btn--number'">
                      </button>
                    </template>
                  </li>
                </template>

                <!-- Next Button -->
                <li>
                  <button @click="goToPage(currentPage + 1)"
                          :disabled="currentPage === totalPages"
                          :class="currentPage === totalPages ? 'gaming-pagination-btn gaming-pagination-btn--disabled' : 'gaming-pagination-btn gaming-pagination-btn--nav'"
                          class="gaming-pagination-next">
                    <span class="gaming-pagination-text hidden sm:inline">Sau</span>
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" class="gaming-pagination-icon">
                      <path d="M9 5L15 12L9 19" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"></path>
                    </svg>
                  </button>
                </li>
              </ul>

              <!-- Gaming Pagination Info -->
              <div class="gaming-pagination-info">
                <span class="gaming-pagination-info-text">
                  Trang <span class="gaming-pagination-info-current" x-text="currentPage"></span>
                  trên <span class="gaming-pagination-info-total" x-text="totalPages"></span>
                </span>
              </div>
            </div>
          </div>
        </template>
      </div>

    </div>
  </div>
</div>

<script>
  // Gaming Triangle Color Generator
  function getRandomTriangleColor() {
    const gamingColors = [
      '#5081FF', // Primary Blue
      '#3463DB', // Secondary Blue
      '#4B7DFF', // Accent Blue
      '#6366F1', // Indigo
      '#8B5CF6', // Purple
      '#06B6D4', // Cyan
      '#10B981', // Emerald
      '#F59E0B', // Amber
      '#EF4444', // Red
      '#EC4899'  // Pink
    ];
    return gamingColors[Math.floor(Math.random() * gamingColors.length)];
  }

  function gamesData() {
    return {
      games: [],
      loading: true,
      params: {},
      currentPage: 1,
      totalPages: 0,
      pages: [],

      init() {
  // Đọc URL hiện tại để lấy page
  const urlParams = new URLSearchParams(window.location.search);
  this.params = Object.fromEntries(urlParams.entries());
  this.currentPage = parseInt(this.params.page) || 1;
  this.loadData();

  // Lắng nghe popstate (khi người dùng bấm Back/Forward)
  window.addEventListener('popstate', () => {
    const urlParamsPop = new URLSearchParams(window.location.search);
    this.params = Object.fromEntries(urlParamsPop.entries());
    this.currentPage = parseInt(this.params.page) || 1;
    this.loadData(); // Gọi lại loadData() để cập nhật giao diện
  });

  // Lắng nghe submit của form lọc (như cũ)
  const form = document.getElementById('searchCard');
  if (form) {
    form.addEventListener('submit', (e) => {
      e.preventDefault();
      this.updateParamsFromForm();
      this.params.page = 1;
      this.currentPage = 1;
      this.loadData();
      history.pushState(null, '', '?' + new URLSearchParams(this.params).toString());
    });
  }
},
      updateParamsFromForm() {
        const formData = new FormData(document.getElementById('searchCard'));
        let newParams = {};
        for (const [key, value] of formData.entries()) {
          // Nếu là select multiple có thể cần xử lý mảng
          if (newParams[key]) {
            if(Array.isArray(newParams[key])) {
              newParams[key].push(value);
            } else {
              newParams[key] = [newParams[key], value];
            }
          } else {
            newParams[key] = value;
          }
        }
        this.params = newParams;
      },
      loadData() {
        this.loading = true;
        fetch('/api<?=home_uri() ?>', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify(this.params)
        })
        .then(response => response.json())
        .then(data => {
          console.log('API Response:', data);
          this.games = data.games;
          this.currentPage = parseInt(data.page) || 1;
          this.totalPages = parseInt(data.nums) || 0;
          console.log('Pagination Data - Current Page:', this.currentPage, 'Total Pages:', this.totalPages);
          this.buildPages();
          console.log('Pages Array:', this.pages);
        })
        .catch(error => console.error('Error fetching data:', error))
        .finally(() => {
          this.loading = false;
        });
      },
      buildPages() {
        this.pages = [];
        const total = this.totalPages;
        const current = this.currentPage;

        console.log('Building pages - Current:', current, 'Total:', total);

        // Handle edge cases
        if (total <= 0) return;
        if (total === 1) {
          this.pages.push(1);
          return;
        }

        // Calculate available slots for page numbers
        // Total elements = Prev + Page Numbers + Next = 7 max
        // So we have 5 slots for page numbers and ellipsis (7 - 2 navigation buttons)
        const maxPageSlots = 5;

        // For small page counts, show all pages (no ellipsis needed)
        if (total <= maxPageSlots) {
          for (let i = 1; i <= total; i++) {
            this.pages.push(i);
          }
          return;
        }

        // For larger page counts, implement smart 7-element pagination
        // Strategy: Optimize for 5 page slots including ellipsis

        if (current <= 3) {
          // Current page is near the beginning (1, 2, or 3)
          // Pattern: [1] [2] [3] [4] [...] (4 elements + prev/next = 6 total)
          // Or: [1] [2] [3] [...] [total] (5 elements + prev/next = 7 total)
          for (let i = 1; i <= Math.min(4, total - 1); i++) {
            this.pages.push(i);
          }
          if (total > 4) {
            this.pages.push('...');
            this.pages.push(total);
          }
        } else if (current >= total - 2) {
          // Current page is near the end (last-2, last-1, or last)
          // Pattern: [1] [...] [total-3] [total-2] [total-1] [total] (6 elements + prev/next = 8)
          // Optimized: [1] [...] [total-2] [total-1] [total] (5 elements + prev/next = 7)
          this.pages.push(1);
          this.pages.push('...');
          for (let i = Math.max(total - 2, 2); i <= total; i++) {
            this.pages.push(i);
          }
        } else {
          // Current page is in the middle
          // Pattern: [1] [...] [current-1] [current] [current+1] [...] [total] (7 elements)
          // But this exceeds our limit, so optimize to: [1] [...] [current] [...] [total] (5 elements)
          this.pages.push(1);

          // Check if we can fit current with neighbors
          if (current - 1 > 2) {
            this.pages.push('...');
          }

          // Add current page with one neighbor if possible
          const canAddNeighbors = this.pages.length < 3; // We have room for current + neighbors
          if (canAddNeighbors && current > 2) {
            this.pages.push(current - 1);
          }
          this.pages.push(current);
          if (canAddNeighbors && current < total - 1) {
            this.pages.push(current + 1);
          }

          // Add ellipsis and last page if needed
          const needsEndEllipsis = current + 1 < total - 1 || (!canAddNeighbors && current < total - 1);
          if (needsEndEllipsis && this.pages.length < maxPageSlots - 1) {
            this.pages.push('...');
          }

          if (!this.pages.includes(total) && this.pages.length < maxPageSlots) {
            this.pages.push(total);
          }
        }

        // Ensure we never exceed 5 page elements (leaving 2 for prev/next)
        if (this.pages.length > maxPageSlots) {
          console.warn('Page array exceeds maximum slots, truncating:', this.pages);
          this.pages = this.pages.slice(0, maxPageSlots);
        }

        console.log('Generated pages array (max 7 total with nav):', this.pages);
        console.log('Total elements will be:', this.pages.length + 2, '(including prev/next)');
      },
      goToPage(page) {
        if (typeof page !== 'number' || isNaN(page)) return; // bỏ qua nếu không phải số
        if (page < 1 || page > this.totalPages) return;
        this.params.page = page;
        this.currentPage = page;
        history.pushState(null, '', '?' + new URLSearchParams(this.params).toString());
        this.loadData();
      }
    }
  }
</script>


<!-- Khởi tạo select2 cho các select multiple -->
<script>
  const char_map = document.getElementById('char_map');
  const weap_map = document.getElementById('weap_map');
  const skin_map = document.getElementById('skin_map');

  // Enhanced Gaming Select2 Configuration for Multiple Selection
  const gamingSelect2Config = {
    allowClear: true,
    width: '100%',
    minimumResultsForSearch: 5,
    dropdownCssClass: 'gaming-select2-dropdown',
    selectionCssClass: 'gaming-select2-selection',
    theme: 'default',
    closeOnSelect: false, // Keep dropdown open for multiple selections
    escapeMarkup: function(markup) { return markup; }, // Allow HTML in options
    templateResult: function(data) {
      if (!data.id) return data.text;
      return $('<span class="select2-option-text">' + data.text + '</span>');
    },
    templateSelection: function(data) {
      if (!data.id) return data.text;
      return $('<span class="select2-selection-text">' + data.text + '</span>');
    }
  };

  // Enhanced function to force gaming styling after Select2 initialization
  function forceGamingStyle(element) {
    setTimeout(() => {
      const container = element.next('.select2-container');
      const selection = container.find('.select2-selection');
      const isMultiple = element.attr('multiple');

      // Force gaming background and styling
      selection.css({
        'background-color': 'rgba(30, 41, 59, 0.5)',
        'background': 'rgba(30, 41, 59, 0.5)',
        'border': '1px solid rgba(80, 129, 255, 0.3)',
        'border-radius': '0.5rem',
        'color': '#ffffff',
        'min-height': isMultiple ? '48px' : '48px',
        'height': isMultiple ? 'auto' : '48px'
      });

      // Force text color
      selection.find('.select2-selection__rendered').css('color', '#ffffff');
      selection.find('.select2-selection__placeholder').css('color', '#9CA3AF');

      // Enhanced styling for multiple selection tags
      if (isMultiple) {
        selection.find('.select2-selection__choice').each(function() {
          $(this).css({
            'background-color': '#5081FF',
            'border': '1px solid #3463DB',
            'border-radius': '0.375rem',
            'color': '#ffffff',
            'font-size': '0.875rem',
            'padding': '0.25rem 0.5rem',
            'margin': '0.125rem'
          });
        });

        // Style remove buttons
        selection.find('.select2-selection__choice__remove').css({
          'color': '#ffffff',
          'margin-right': '0.25rem',
          'font-weight': 'bold'
        });
      }
    }, 300); // Increased timeout for better reliability
  }

  // Debug function to check data
  function debugSelect2Data(elementId, data) {
    console.log(`${elementId} data:`, data);
    console.log(`${elementId} data length:`, data ? data.length : 'undefined');
  }

  // Initialize Select2 elements with HTML-based approach
  $(document).ready(function() {
    // Debug data for troubleshooting
    const charData = <?php echo json_encode($char_datas); ?>;
    const weapData = <?php echo json_encode($weap_datas); ?>;
    const skinData = <?php echo json_encode($skin_datas); ?>;

    debugSelect2Data('char_map', charData);
    debugSelect2Data('weap_map', weapData);
    debugSelect2Data('skin_map', skinData);

    // Initialize all Select2 elements using HTML options (more reliable)
    $('.select2').each(function() {
      if (!$(this).hasClass('select2-hidden-accessible')) {
        try {
          const elementId = $(this).attr('id');
          const isMultiple = $(this).attr('multiple');
          let config = { ...gamingSelect2Config };

          // Add specific placeholder for each element
          if (elementId === 'char_map') {
            config.placeholder = "Chọn <?php echo $game['char'] ?>";
          } else if (elementId === 'weap_map') {
            config.placeholder = "Chọn <?php echo $game['weapon'] ?>";
          } else if (elementId === 'skin_map') {
            config.placeholder = "Chọn <?php echo $game['skin'] ?>";
          }

          // Enhanced configuration for multiple selects
          if (isMultiple) {
            config.maximumSelectionLength = 10; // Limit selections to prevent UI overflow
            config.tags = false; // Disable tag creation
            config.tokenSeparators = [',', ' ']; // Allow comma/space separation
          }

          const select2Instance = $(this).select2(config);

          // Enhanced event handlers for better UX
          select2Instance.on('select2:select', function(e) {
            forceGamingStyle($(this));
            console.log(`Selected: ${e.params.data.text} in ${elementId}`);
          });

          select2Instance.on('select2:unselect', function(e) {
            forceGamingStyle($(this));
            console.log(`Unselected: ${e.params.data.text} in ${elementId}`);
          });

          select2Instance.on('select2:open', function() {
            // Ensure dropdown is properly positioned and styled
            $('.gaming-select2-dropdown').css('z-index', '9999');
          });

          forceGamingStyle($(this));
          console.log(`${elementId} Select2 initialized successfully`);
        } catch (error) {
          console.error('Error initializing Select2 for element:', this.id, error);
        }
      }
    });

    // Additional verification - check if options are loaded
    setTimeout(() => {
      $('.select2').each(function() {
        const elementId = $(this).attr('id');
        const optionCount = $(this).find('option').length;
        console.log(`${elementId} has ${optionCount} options`);

        if (optionCount === 0) {
          console.warn(`${elementId} has no options - this might indicate a data loading issue`);
        }
      });
    }, 500);
  });
</script>

<!-- Các script xử lý hiển thị form lọc -->
<script>
  let isSearchCardVisible = false;
  function adjustSearchCardDisplay() {
    const searchCard = document.getElementById('searchCard');
    if (window.innerWidth > 768) {
      searchCard.style.display = 'block';
      isSearchCardVisible = true;
    } else if (!isSearchCardVisible) {
      searchCard.style.display = 'none';
    }
  }
  function toggleSearchCard() {
    const searchCard = document.getElementById('searchCard');
    if (searchCard.style.display === 'none') {
      searchCard.style.display = 'block';
      isSearchCardVisible = true;
    } else {
      searchCard.style.display = 'none';
      isSearchCardVisible = false;
    }
  }
  function preventAutoHideOnInputFocus() {
    const searchCard = document.getElementById('searchCard');
    const inputs = searchCard.querySelectorAll('input, select');
    inputs.forEach(function(input) {
      input.addEventListener('focus', function() {
        searchCard.style.display = 'block';
        isSearchCardVisible = true;
      });
    });
  }
  window.onload = function() {
    adjustSearchCardDisplay();
    preventAutoHideOnInputFocus();
  };
  window.addEventListener('resize', function() {
    if (window.innerHeight > 400) {
      adjustSearchCardDisplay();
    }
  });
  document.getElementById('toggleSearchButton').addEventListener('click', toggleSearchCard);
</script>

<!-- Hàm marqueeData (giữ nguyên như cũ) -->
<script>
  function marqueeData(images) {
    return {
      images: images,
      bg: '/assets/image/d7bb309392a99e4179abbf83cba73c3d.png',
      speed: 50,
      duration: 10,
      isOverflow: true,
      init() {
        this.$nextTick(() => {
          setTimeout(() => {
            let track = this.$refs.track;
            let copy = this.$refs.copy;
            let container = this.$el;
            if (copy) {
              let copyWidth = copy.offsetWidth;
              let containerWidth = container.offsetWidth;
              if (copyWidth <= containerWidth) {
                this.isOverflow = false;
                track.style.animation = 'none';
              } else {
                this.isOverflow = true;
                this.duration = copyWidth / this.speed;
                track.style.setProperty('--move-distance', copyWidth + 'px');
              }
            }
          }, 500);
        });
      }
    }
  }
</script>

<!-- Gaming Triangle Color Function -->
<script>
  function getRandomTriangleColor() {
    const gamingColors = [
      '#5081FF', // Gaming Blue
      '#3463DB', // Deep Blue
      '#7C3AED', // Purple
      '#10B981', // Emerald
      '#F59E0B', // Amber
      '#EF4444', // Red
      '#8B5CF6', // Violet
      '#06B6D4', // Cyan
      '#84CC16', // Lime
      '#F97316'  // Orange
    ];
    return gamingColors[Math.floor(Math.random() * gamingColors.length)];
  }
</script>

<style>
  /* Gaming Select2 Styling - Enhanced with Higher Specificity */

  /* Force gaming background on all Select2 containers */
  .select2-container .select2-selection--single,
  .select2-container .select2-selection--multiple,
  .select2-container--gaming .select2-selection--single,
  .select2-container--gaming .select2-selection--multiple,
  .select2-container--default .select2-selection--single,
  .select2-container--default .select2-selection--multiple {
    height: 48px !important;
    min-height: 48px !important;
    background-color: rgba(30, 41, 59, 0.5) !important;
    background: rgba(30, 41, 59, 0.5) !important;
    border: 1px solid rgba(80, 129, 255, 0.3) !important;
    border-radius: 0.5rem !important;
    padding: 0.75rem 1rem !important;
    color: #ffffff !important;
    font-family: 'Signika', ui-sans-serif, system-ui, sans-serif !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    box-sizing: border-box !important;
  }

  /* Additional override for any white background */
  .select2-container .select2-selection {
    background-color: rgba(30, 41, 59, 0.5) !important;
    background: rgba(30, 41, 59, 0.5) !important;
  }

  .select2-container .select2-selection--multiple,
  .select2-container--gaming .select2-selection--multiple,
  .select2-container--default .select2-selection--multiple {
    min-height: 48px !important;
    max-height: 120px !important; /* Limit height for better UX */
    overflow-y: auto !important; /* Allow scrolling for many selections */
    padding: 0.375rem 0.75rem !important;
    background-color: rgba(30, 41, 59, 0.5) !important;
    background: rgba(30, 41, 59, 0.5) !important;
    display: flex !important;
    flex-wrap: wrap !important;
    align-items: flex-start !important;
    gap: 0.25rem !important;
  }

  /* Enhanced scrollbar for multiple selection container */
  .select2-container .select2-selection--multiple::-webkit-scrollbar {
    width: 4px !important;
  }

  .select2-container .select2-selection--multiple::-webkit-scrollbar-track {
    background: rgba(30, 41, 59, 0.3) !important;
    border-radius: 2px !important;
  }

  .select2-container .select2-selection--multiple::-webkit-scrollbar-thumb {
    background: #5081FF !important;
    border-radius: 2px !important;
  }

  /* Text and content styling */
  .select2-container .select2-selection__rendered,
  .select2-container--gaming .select2-selection__rendered,
  .select2-container--default .select2-selection__rendered {
    color: #ffffff !important;
    line-height: 1.5 !important;
    padding: 0 !important;
    margin: 0 !important;
    display: flex !important;
    align-items: center !important;
    height: 100% !important;
  }

  /* Force override any inherited backgrounds */
  .select2-container .select2-selection__rendered,
  .select2-container .select2-selection__rendered * {
    background-color: transparent !important;
    background: transparent !important;
  }

  /* Placeholder styling */
  .select2-container .select2-selection__placeholder,
  .select2-container--gaming .select2-selection__placeholder,
  .select2-container--default .select2-selection__placeholder {
    color: #9CA3AF !important;
  }

  /* Arrow styling */
  .select2-container .select2-selection__arrow,
  .select2-container--gaming .select2-selection__arrow,
  .select2-container--default .select2-selection__arrow {
    height: 46px !important;
    right: 1rem !important;
    top: 1px !important;
  }

  .select2-container .select2-selection__arrow b,
  .select2-container--gaming .select2-selection__arrow b,
  .select2-container--default .select2-selection__arrow b {
    border-color: #9CA3AF transparent transparent transparent !important;
    border-style: solid !important;
    border-width: 5px 4px 0 4px !important;
    height: 0 !important;
    left: 50% !important;
    margin-left: -4px !important;
    margin-top: -2px !important;
    position: absolute !important;
    top: 50% !important;
    width: 0 !important;
  }

  /* Focus State - Enhanced */
  .select2-container.select2-container--focus .select2-selection,
  .select2-container--gaming.select2-container--focus .select2-selection,
  .select2-container--default.select2-container--focus .select2-selection,
  .select2-container.select2-container--focus .select2-selection--single,
  .select2-container.select2-container--focus .select2-selection--multiple {
    border-color: #5081FF !important;
    box-shadow: 0 0 0 2px rgba(80, 129, 255, 0.2) !important;
    outline: none !important;
    background-color: rgba(30, 41, 59, 0.5) !important;
    background: rgba(30, 41, 59, 0.5) !important;
  }

  /* Hover State - Enhanced */
  .select2-container .select2-selection:hover,
  .select2-container .select2-selection--single:hover,
  .select2-container .select2-selection--multiple:hover {
    border-color: #5081FF !important;
    background-color: rgba(30, 41, 59, 0.5) !important;
    background: rgba(30, 41, 59, 0.5) !important;
  }

  /* Enhanced Multiple Selection Tags */
  .select2-container .select2-selection__choice,
  .select2-container--gaming .select2-selection__choice,
  .select2-container--default .select2-selection__choice {
    background: linear-gradient(135deg, #5081FF, #3463DB) !important;
    border: 1px solid #3463DB !important;
    border-radius: 0.5rem !important;
    color: #ffffff !important;
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    padding: 0.375rem 0.75rem !important;
    margin: 0.125rem !important;
    display: inline-flex !important;
    align-items: center !important;
    gap: 0.375rem !important;
    transition: all 0.2s ease-in-out !important;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1) !important;
    max-width: 200px !important; /* Prevent overly long tags */
    overflow: hidden !important;
    text-overflow: ellipsis !important;
    white-space: nowrap !important;
  }

  .select2-container .select2-selection__choice:hover,
  .select2-container--gaming .select2-selection__choice:hover,
  .select2-container--default .select2-selection__choice:hover {
    background: linear-gradient(135deg, #4B7DFF, #2A52BE) !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(80, 129, 255, 0.3) !important;
  }

  /* Enhanced Remove Button Styling */
  .select2-container .select2-selection__choice__remove,
  .select2-container--gaming .select2-selection__choice__remove,
  .select2-container--default .select2-selection__choice__remove {
    color: #ffffff !important;
    font-size: 1rem !important;
    font-weight: bold !important;
    margin-right: 0 !important;
    margin-left: 0.25rem !important;
    padding: 0.125rem !important;
    border-radius: 50% !important;
    width: 18px !important;
    height: 18px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    transition: all 0.2s ease-in-out !important;
    background: rgba(255, 255, 255, 0.1) !important;
  }

  .select2-container .select2-selection__choice__remove:hover,
  .select2-container--gaming .select2-selection__choice__remove:hover,
  .select2-container--default .select2-selection__choice__remove:hover {
    color: #ffffff !important;
    background: #EF4444 !important;
    transform: scale(1.1) !important;
  }

  /* Enhanced Dropdown Styling */
  .gaming-select2-dropdown {
    background: linear-gradient(145deg, #1E293B 0%, #0F172A 100%) !important;
    border: 2px solid rgba(80, 129, 255, 0.4) !important;
    border-radius: 0.75rem !important;
    box-shadow:
      0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04),
      0 0 30px rgba(80, 129, 255, 0.4),
      inset 0 1px 0 rgba(255, 255, 255, 0.1) !important;
    margin-top: 0.5rem !important;
    z-index: 99999 !important;
    backdrop-filter: blur(10px) !important;
    animation: dropdown-appear 0.2s ease-out !important;
  }

  @keyframes dropdown-appear {
    from {
      opacity: 0;
      transform: translateY(-10px) scale(0.95);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  .gaming-select2-dropdown .select2-results__options {
    max-height: 250px !important;
    overflow-y: auto !important;
    scrollbar-width: thin !important;
    scrollbar-color: #5081FF #1E293B !important;
    padding: 0.25rem !important;
  }

  .gaming-select2-dropdown .select2-results__options::-webkit-scrollbar {
    width: 6px !important;
  }

  .gaming-select2-dropdown .select2-results__options::-webkit-scrollbar-track {
    background: #1E293B !important;
  }

  .gaming-select2-dropdown .select2-results__options::-webkit-scrollbar-thumb {
    background: #5081FF !important;
    border-radius: 3px !important;
  }

  .gaming-select2-dropdown .select2-results__option {
    color: #ffffff !important;
    padding: 0.875rem 1.25rem !important;
    font-family: 'Signika', ui-sans-serif, system-ui, sans-serif !important;
    font-size: 0.9rem !important;
    font-weight: 500 !important;
    border-radius: 0.5rem !important;
    margin: 0.125rem 0 !important;
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
    cursor: pointer !important;
    position: relative !important;
    overflow: hidden !important;
  }

  .gaming-select2-dropdown .select2-results__option::before {
    content: '' !important;
    position: absolute !important;
    left: 0 !important;
    top: 0 !important;
    height: 100% !important;
    width: 3px !important;
    background: transparent !important;
    transition: all 0.2s ease-in-out !important;
  }

  .gaming-select2-dropdown .select2-results__option--highlighted {
    background: linear-gradient(135deg, rgba(80, 129, 255, 0.3), rgba(52, 99, 219, 0.2)) !important;
    color: #ffffff !important;
    transform: translateX(4px) !important;
  }

  .gaming-select2-dropdown .select2-results__option--highlighted::before {
    background: #5081FF !important;
  }

  .gaming-select2-dropdown .select2-results__option[aria-selected="true"] {
    background: linear-gradient(135deg, #5081FF, #3463DB) !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    box-shadow: 0 2px 4px rgba(80, 129, 255, 0.3) !important;
  }

  .gaming-select2-dropdown .select2-results__option[aria-selected="true"]::before {
    background: #ffffff !important;
  }

  .gaming-select2-dropdown .select2-results__option[aria-selected="true"]::after {
    content: '✓' !important;
    position: absolute !important;
    right: 1rem !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    color: #ffffff !important;
    font-weight: bold !important;
    font-size: 1rem !important;
  }

  /* Enhanced Search Input */
  .gaming-select2-dropdown .select2-search {
    padding: 0.75rem !important;
    border-bottom: 1px solid rgba(80, 129, 255, 0.2) !important;
    margin-bottom: 0.5rem !important;
  }

  .gaming-select2-dropdown .select2-search__field {
    background: linear-gradient(145deg, rgba(30, 41, 59, 0.9), rgba(15, 23, 42, 0.9)) !important;
    border: 2px solid rgba(80, 129, 255, 0.3) !important;
    border-radius: 0.5rem !important;
    color: #ffffff !important;
    padding: 0.75rem 1rem !important;
    margin: 0 !important;
    width: 100% !important;
    font-family: 'Signika', ui-sans-serif, system-ui, sans-serif !important;
    font-size: 0.9rem !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1) !important;
  }

  .gaming-select2-dropdown .select2-search__field::placeholder {
    color: #9CA3AF !important;
    font-style: italic !important;
  }

  .gaming-select2-dropdown .select2-search__field:focus {
    border-color: #5081FF !important;
    outline: none !important;
    box-shadow:
      0 0 0 3px rgba(80, 129, 255, 0.2),
      inset 0 2px 4px rgba(0, 0, 0, 0.1),
      0 4px 6px rgba(80, 129, 255, 0.1) !important;
    background: linear-gradient(145deg, rgba(30, 41, 59, 1), rgba(15, 23, 42, 1)) !important;
    transform: translateY(-1px) !important;
  }

  /* Clear Button */
  .select2-container--gaming .select2-selection__clear {
    color: #9CA3AF !important;
    font-size: 1.2em !important;
    font-weight: bold !important;
    margin-right: 0.5rem !important;
  }

  .select2-container--gaming .select2-selection__clear:hover {
    color: #EF4444 !important;
  }

  /* Enhanced Responsive Adjustments */
  @media (max-width: 768px) {
    .select2-container .select2-selection--single,
    .select2-container .select2-selection--multiple,
    .select2-container--gaming .select2-selection--single,
    .select2-container--gaming .select2-selection--multiple,
    .select2-container--default .select2-selection--single,
    .select2-container--default .select2-selection--multiple {
      min-height: 44px !important;
      padding: 0.625rem 0.875rem !important;
      font-size: 0.875rem !important;
    }

    .select2-container .select2-selection--multiple,
    .select2-container--gaming .select2-selection--multiple,
    .select2-container--default .select2-selection--multiple {
      max-height: 100px !important;
      gap: 0.125rem !important;
    }

    .select2-container .select2-selection__choice,
    .select2-container--gaming .select2-selection__choice,
    .select2-container--default .select2-selection__choice {
      font-size: 0.8125rem !important;
      padding: 0.25rem 0.5rem !important;
      max-width: 150px !important;
    }

    .select2-container .select2-selection__choice__remove,
    .select2-container--gaming .select2-selection__choice__remove,
    .select2-container--default .select2-selection__choice__remove {
      width: 16px !important;
      height: 16px !important;
      font-size: 0.875rem !important;
    }

    .select2-container .select2-selection__arrow,
    .select2-container--gaming .select2-selection__arrow,
    .select2-container--default .select2-selection__arrow {
      height: 42px !important;
      right: 0.875rem !important;
    }

    .gaming-select2-dropdown {
      border-radius: 0.5rem !important;
      margin-top: 0.25rem !important;
    }

    .gaming-select2-dropdown .select2-results__options {
      max-height: 200px !important;
    }

    .gaming-select2-dropdown .select2-results__option {
      padding: 0.75rem 1rem !important;
      font-size: 0.875rem !important;
    }

    .gaming-select2-dropdown .select2-search__field {
      padding: 0.625rem 0.875rem !important;
      font-size: 0.875rem !important;
    }
  }

  /* Extra small devices */
  @media (max-width: 480px) {
    .select2-container .select2-selection--multiple,
    .select2-container--gaming .select2-selection--multiple,
    .select2-container--default .select2-selection--multiple {
      max-height: 80px !important;
    }

    .select2-container .select2-selection__choice,
    .select2-container--gaming .select2-selection__choice,
    .select2-container--default .select2-selection__choice {
      max-width: 120px !important;
      font-size: 0.75rem !important;
    }

    .gaming-select2-dropdown .select2-results__options {
      max-height: 180px !important;
    }
  }

  /* Additional Gaming Animations */
  @keyframes gaming-card-glow {
    0%, 100% { box-shadow: 0 20px 40px rgba(80, 129, 255, 0.4), 0 0 30px rgba(80, 129, 255, 0.3), 0 0 60px rgba(75, 125, 255, 0.2); }
    50% { box-shadow: 0 20px 40px rgba(80, 129, 255, 0.6), 0 0 30px rgba(80, 129, 255, 0.5), 0 0 60px rgba(75, 125, 255, 0.4); }
  }

  @keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
  }

  @keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
  }

  /* Select2 Loading State */
  .select2-container--gaming.select2-container--loading .select2-selection::after {
    content: '' !important;
    position: absolute !important;
    right: 2.5rem !important;
    top: 50% !important;
    transform: translateY(-50%) !important;
    width: 16px !important;
    height: 16px !important;
    border: 2px solid #5081FF !important;
    border-top: 2px solid transparent !important;
    border-radius: 50% !important;
    animation: spin 1s linear infinite !important;
  }

  @keyframes spin {
    0% { transform: translateY(-50%) rotate(0deg); }
    100% { transform: translateY(-50%) rotate(360deg); }
  }

  /* Marquee Animation */
  @keyframes marquee {
    from { transform: translateX(0); }
    to { transform: translateX(calc(-1 * var(--move-distance))); }
  }
  .marquee-track {
    display: flex;
    white-space: nowrap;
    animation: marquee linear infinite;
  }
  .marquee-track:hover {
    animation-play-state: paused;
  }

  /* Enhanced Focus Management */
  .select2-container--gaming.select2-container--focus .select2-selection,
  .select2-container--gaming.select2-container--open .select2-selection {
    border-color: #5081FF !important;
    box-shadow: 0 0 0 3px rgba(80, 129, 255, 0.2) !important;
  }

  /* Improved Clear Button */
  .select2-container .select2-selection__clear,
  .select2-container--gaming .select2-selection__clear,
  .select2-container--default .select2-selection__clear {
    color: #9CA3AF !important;
    font-size: 1.1em !important;
    font-weight: bold !important;
    margin-right: 0.5rem !important;
    padding: 0.25rem !important;
    border-radius: 50% !important;
    transition: all 0.2s ease-in-out !important;
    background: rgba(255, 255, 255, 0.1) !important;
    width: 20px !important;
    height: 20px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  .select2-container .select2-selection__clear:hover,
  .select2-container--gaming .select2-selection__clear:hover,
  .select2-container--default .select2-selection__clear:hover {
    color: #ffffff !important;
    background: #EF4444 !important;
    transform: scale(1.1) !important;
  }
</style>
 
<?php
    require_once $_SERVER['DOCUMENT_ROOT'] . '/public/includes/footer.php';
?>
