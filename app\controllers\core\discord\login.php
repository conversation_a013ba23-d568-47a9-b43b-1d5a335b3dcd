<?php
function clientDiscord(){
    global $duogxaolin;
    
    // Discord OAuth Configuration
    $client_id = '1383416516149514320'; // Replace with your actual Discord Client ID
    $client_secret = 'bifsbAkoeWqNp7BW5YFfkSj7PnZJR2Fm'; // Replace with your actual Discord Client Secret
    $redirect_uri = home_url().'/discord/api/login'; 
    
    // Discord OAuth URLs
    $auth_url = 'https://discord.com/api/oauth2/authorize';
    $token_url = 'https://discord.com/api/oauth2/token';
    $user_info_url = 'https://discord.com/api/users/@me';
    
    // Build authorization URL
    $params = [
        'client_id' => $client_id,
        'redirect_uri' => $redirect_uri,
        'response_type' => 'code',
        'scope' => 'identify email',
        'state' => md5(uniqid(rand(), true)) // CSRF protection
    ];
    
    $authorization_url = $auth_url . '?' . http_build_query($params);
    
    return [
        'client_id' => $client_id,
        'client_secret' => $client_secret,
        'redirect_uri' => $redirect_uri,
        'auth_url' => $authorization_url,
        'token_url' => $token_url,
        'user_info_url' => $user_info_url
    ];
}

function getDiscordAccessToken($code, $config) {
    $data = [
        'client_id' => $config['client_id'],
        'client_secret' => $config['client_secret'],
        'grant_type' => 'authorization_code',
        'code' => $code,
        'redirect_uri' => $config['redirect_uri']
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $config['token_url']);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/x-www-form-urlencoded'
    ]);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code === 200) {
        return json_decode($response, true);
    }
    
    return false;
}

function getDiscordUserInfo($access_token, $config) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $config['user_info_url']);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Authorization: Bearer ' . $access_token,
        'Content-Type: application/json'
    ]);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code === 200) {
        return json_decode($response, true);
    }
    
    return false;
}

$discord_config = clientDiscord();
$login_discord = $discord_config['auth_url'];
