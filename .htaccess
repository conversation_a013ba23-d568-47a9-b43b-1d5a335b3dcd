RewriteEngine On
RewriteBase /

# --- Home page ---
RewriteRule ^$ public/views/index.php [L]
RewriteRule ^google/api/login$ app/controllers/api/google/login.php [L]


RewriteRule ^recharge$ public/views/recharge/index.php [L]
RewriteRule ^views/recharge$ public/views/recharge/index.php [L]

# --- Customer ---
RewriteRule ^customer$ public/customer/index.php [L]
RewriteRule ^customer/(login|register|logout)$ public/views/customer/$1.php [L]

# --- Shop ---
RewriteRule ^shop/products/([\w@#-]+)$ public/shop/products.php?category=$1 [L]
RewriteRule ^shop/product/([\w@#-]+)$ public/shop/product_detail.php?code=$1 [L]

# --- Seller ---
RewriteRule ^seller$ public/seller/index.php [L]
RewriteRule ^seller/profile$ public/seller/profile.php [L]
RewriteRule ^seller/products$ public/seller/products.php [L]
RewriteRule ^seller/orders$ public/seller/orders.php [L]
RewriteRule ^seller/orders/([\w@#-]+)$ public/seller/orders.php?code=$1 [L]

# --- Recharge ---
RewriteRule ^recharge$ public/recharge/index.php [L]
RewriteRule ^recharge/notify$ public/recharge/notify.php [L]
RewriteRule ^recharge/return$ public/recharge/return.php [L]

# --- Admin ---
RewriteRule ^admin$ public/admin/index.php [L]
RewriteRule ^admin/login$ public/admin/login.php [L]
RewriteRule ^admin/orders$ public/admin/orders.php [L]
RewriteRule ^admin/order/account/([\w@#-]+)$ public/admin/bill/views.php?code=$1 [L]
RewriteRule ^admin/account/view/([\w@#-]+)$ public/admin/bill/views.php?code=$1 [L]

# --- API: Customer ---
RewriteRule ^api/customer/(register|login|logout|info|update|upload|password|notify)$ app/controllers/client/customer/$1.php [L]
RewriteRule ^api/recharge/card$ app/controllers/api/recharge/card.php [L]

RewriteRule ^api/service/([\w@#-]+)/([\w@#-]+)$   app/controllers/api/game/account.php?game=$1&category=$2 [L]
RewriteRule ^api/service/([\w@#-]+)$                     app/controllers/api/service/index.php?type=$1 [L]
# --- API: Game ---
RewriteRule ^api/game/list$ app/controllers/api/game/list.php [L]


# --- API: Seller ---
RewriteRule ^api/seller/(accounts|add|edit|withdraw|category|reroll|product|tools/password)$ app/controllers/seller/service/$1.php [L]

# --- API: Admin ---
RewriteRule ^api/admin/(login|orders|bill)$ app/controllers/admin/service/$1.php [L]

# --- API: Recharge ---
RewriteRule ^api/recharge/(vnpay|momo|callback)$ app/controllers/recharge/service/$1.php [L]

# --- API: Shop ---
RewriteRule ^api/shop/(products|detail|category)$ app/controllers/shop/service/$1.php [L]


# --- API: Tools ---
RewriteRule ^api/profile/avatar$ app/controllers/api/tools/profile.php [L]

RewriteRule ^service/([\w@#-]+)/([\w@#-]+)$   public/views/services/index.php?game=$1&category=$2 [QSA,L]
RewriteRule ^account/([\w@#-]+)$              public/views/services/account.php?slug=$1 [QSA,L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule \.(png|jpeg|jpg|webp|webm|gif|svg)$ /assets/image/error_img.png [L]
# --- Security (khuyến nghị thêm nếu chưa có) ---
<FilesMatch "\.(htaccess|htpasswd|ini|phps|fla|psd|log|sh)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>
