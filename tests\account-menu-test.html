<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Account Menu Test</title>
    <script src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js" defer></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        /* Mobile Navigation Styles */
        .mobile-nav-container {
            position: fixed;
            width: 100%;
            height: 64px;
            bottom: 0;
            left: 0;
            z-index: 999998;
        }

        .nav-section-left {
            float: left;
            width: calc(50% - 40px);
            height: 64px;
            background: #13112E;
            border-top: 1px solid #5081FF33;
            border-top-right-radius: 20px;
            box-shadow: 0 -4px 8px 0 rgba(80, 129, 255, 0.15);
            display: flex;
        }

        .nav-section-right {
            float: right;
            width: calc(50% - 40px);
            height: 64px;
            background: #13112E;
            border-top: 1px solid #5081FF33;
            border-top-left-radius: 20px;
            box-shadow: 0 -4px 8px 0 rgba(80, 129, 255, 0.15);
            display: flex;
        }

        .nav-item {
            width: 50%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding-top: 8px;
            color: #9F9BAB;
            text-decoration: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .nav-item:hover {
            color: #FFFFFFCC;
            transform: translateY(-1px);
        }

        .nav-icon {
            font-size: 26px;
            height: 26px;
            display: block;
            margin: 0 auto 6px;
            width: 26px;
            transition: transform 0.3s ease;
        }

        .nav-item:hover .nav-icon {
            transform: scale(1.1);
        }

        .nav-label {
            display: block;
            height: 16px;
            line-height: 16px;
            margin-top: 2px;
            font-size: 12px;
            font-weight: 500;
            letter-spacing: 0.3px;
        }

        .floating-home-button {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            width: 80px;
            height: 80px;
            background: transparent;
            border-radius: 50%;
            top: -26px;
            box-sizing: border-box;
            padding: 5px;
            z-index: 999999;
            text-decoration: none;
            transition: transform 0.3s ease;
        }

        .floating-home-button::before {
            content: "";
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            background: #13112E;
            border-radius: 50%;
            box-shadow: 0 33px 0 10px #13112E;
            z-index: -1;
        }

        .home-button-inner {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #4B7DFF 0%, #5081FF 100%);
            border-radius: 50%;
            box-sizing: border-box;
            text-align: center;
            cursor: pointer;
            color: #fff;
            font-size: 24px;
            box-shadow:
                0 8px 16px rgba(75, 125, 255, 0.3),
                inset 0 -4px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }

        .mobile-nav-container::after {
            content: "";
            display: table;
            clear: both;
        }
    </style>
</head>
<body class="bg-[#0E0A2F] text-white min-h-screen">
    <div class="p-8">
        <h1 class="text-2xl font-bold mb-4">Account Menu Test</h1>
        <p class="mb-4">Click the "Tài Khoản" button in the bottom navigation to test the account menu.</p>
        
        <div class="mb-4">
            <p>Alpine.js Debug Info:</p>
            <div x-data="{ sidebar: false, account: false }" class="p-4 bg-gray-800 rounded">
                <p>Sidebar: <span x-text="sidebar"></span></p>
                <p>Account: <span x-text="account"></span></p>
                <button @click="account = !account" class="bg-blue-500 px-4 py-2 rounded mt-2">
                    Toggle Account (Test Button)
                </button>
            </div>
        </div>
    </div>

    <!-- Mobile Navigation (Test Implementation) -->
    <div x-data="{ sidebar: false, account: false }" class="mobile-nav-container fixed bottom-0 w-full h-[64px] z-[999998] lg:hidden">
        <!-- Left Section -->
        <div class="nav-section-left">
            <!-- Danh Mục (Sidebar Toggle) -->
            <div @click="sidebar = !sidebar" class="nav-item">
                <div class="nav-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 25 24" fill="none">
                        <path d="M4 6h16M4 12h16M4 18h16" stroke="currentColor" stroke-width="1.5"></path>
                    </svg>
                </div>
                <span class="nav-label">Danh Mục</span>
            </div>

            <!-- Nạp Tiền -->
            <div class="nav-item">
                <div class="nav-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 25 24" fill="none">
                        <path d="M15.32 9.333a2 2 0 0 0-.626-.942A1.7 1.7 0 0 0 13.7 8h-2.4c-.477 0-.935.21-1.273.586A2.12 2.12 0 0 0 9.5 10c0 .53.19 1.04.527 1.414.338.375.796.586 1.273.586h2.4c.477 0 .935.21 1.273.586.337.375.527.884.527 1.414s-.19 1.04-.527 1.414A1.7 1.7 0 0 1 13.7 16h-2.4a1.7 1.7 0 0 1-.994-.391 2 2 0 0 1-.626-.942M12.5 6v2m0 8v2" stroke="currentColor" stroke-width="1.5"></path>
                        <path d="M3.5 12a9 9 0 1 0 18.001 0A9 9 0 0 0 3.5 12Z" stroke="currentColor" stroke-width="1.5"></path>
                    </svg>
                </div>
                <span class="nav-label">Nạp Tiền</span>
            </div>
        </div>

        <!-- Floating Home Button -->
        <a href="/" class="floating-home-button">
            <div class="home-button-inner">
                <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none">
                    <path d="M3 9.5 12 3l9 6.5V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z" stroke="currentColor" stroke-width="1.5"></path>
                    <path d="M9 22V12h6v10" stroke="currentColor" stroke-width="1.5"></path>
                </svg>
            </div>
        </a>

        <!-- Right Section -->
        <div class="nav-section-right">
            <!-- Kho Hàng -->
            <div class="nav-item">
                <div class="nav-icon">
                    <svg width="1em" height="1em" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M2 9.75A.75.75 0 0 1 2.75 9h18a.75.75 0 0 1 .75.75v8a2.75 2.75 0 0 1-2.75 2.75h-14A2.75 2.75 0 0 1 2 17.75zm1.5.75v7.25c0 .69.56 1.25 1.25 1.25h14c.69 0 1.25-.56 1.25-1.25V10.5z" fill="currentColor"></path>
                        <path d="M2 7.75A3.75 3.75 0 0 1 5.75 4h12a3.75 3.75 0 0 1 3.75 3.75v2a.75.75 0 0 1-.75.75h-18A.75.75 0 0 1 2 9.75zM5.75 5.5A2.25 2.25 0 0 0 3.5 7.75V9H20V7.75a2.25 2.25 0 0 0-2.25-2.25z" fill="currentColor"></path>
                    </svg>
                </div>
                <span class="nav-label">Kho Hàng</span>
            </div>

            <!-- Tài Khoản -->
            <div @click="account = !account" class="nav-item cursor-pointer">
                <div class="nav-icon">
                    <svg xmlns="http://www.w3.org/2000/svg" width="1em" height="1em" viewBox="0 0 24 24" fill="none">
                        <path d="M3 12a9 9 0 1 0 18.001 0A9 9 0 0 0 3 12Z" stroke="currentColor" stroke-width="1.5"></path>
                        <path d="M9 10a3 3 0 1 0 6 0 3 3 0 0 0-6 0Zm-2.832 8.849A4 4 0 0 1 10 16h4a4 4 0 0 1 3.834 2.855" stroke="currentColor" stroke-width="1.5"></path>
                    </svg>
                </div>
                <span class="nav-label">Tài Khoản</span>
            </div>
        </div>

        <!-- Mobile Account Panel Overlay -->
        <div x-show="account"
             class="fixed inset-0 z-[1000000] bg-black/80 transition-opacity duration-300 ease-in-out lg:hidden"
             style="display: none;"
             @click="account = false"
             x-transition:enter="transition ease-out duration-300"
             x-transition:enter-start="opacity-0"
             x-transition:enter-end="opacity-100"
             x-transition:leave="transition ease-in duration-200"
             x-transition:leave-start="opacity-100"
             x-transition:leave-end="opacity-0"></div>

        <!-- Mobile Account Panel - Right Sliding -->
        <div x-show="account"
             class="fixed right-0 top-0 h-full w-[80%] bg-[#0E0A2F] border-l border-[#5081ff33] shadow-lg z-[1000001] lg:hidden overflow-y-auto"
             style="display: none;"
             x-transition:enter="transform transition-transform duration-500 ease-in-out"
             x-transition:enter-start="translate-x-full opacity-0"
             x-transition:enter-end="translate-x-0 opacity-100"
             x-transition:leave="transform transition-transform duration-500 ease-in-out"
             x-transition:leave-start="translate-x-0 opacity-100"
             x-transition:leave-end="translate-x-full opacity-0">

            <!-- Header Section with Close Button -->
            <div class="sticky top-0 bg-[#0E0A2F] border-b border-[#5081ff33] p-4 z-10">
                <div class="flex items-center justify-between">
                    <h2 class="text-lg font-semibold text-white">Tài Khoản</h2>
                    <button @click="account = false" class="p-2 text-[#FFFFFF99] hover:text-[#FFFFFFCC] hover:bg-[#FFFFFF1F] rounded-lg transition-colors duration-300">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-6 w-6">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>

            <!-- Account Content -->
            <div class="p-6">
                <!-- Test Content -->
                <div class="text-center py-8">
                    <div class="w-20 h-20 bg-[#4B7DFF]/20 rounded-full flex items-center justify-center mx-auto mb-4">
                        <svg xmlns="http://www.w3.org/2000/svg" width="32" height="32" viewBox="0 0 24 24" fill="none" class="text-[#4B7DFF]">
                            <path d="M3 12a9 9 0 1 0 18.001 0A9 9 0 0 0 3 12Z" stroke="currentColor" stroke-width="1.5"></path>
                            <path d="M9 10a3 3 0 1 0 6 0 3 3 0 0 0-6 0Zm-2.832 8.849A4 4 0 0 1 10 16h4a4 4 0 0 1 3.834 2.855" stroke="currentColor" stroke-width="1.5"></path>
                        </svg>
                    </div>
                    <h3 class="text-white text-lg font-semibold mb-2">Account Menu Test</h3>
                    <p class="text-[#FFFFFF99] mb-6">The account menu is working correctly!</p>
                    <button @click="account = false"
                           class="inline-block bg-gradient-to-r from-[#4B7DFF] via-[#5081FF] to-[#4B7DFF] hover:from-[#5081FF] hover:via-[#4B7DFF] hover:to-[#5081FF] text-white font-bold py-3 px-6 rounded-lg transition-all duration-500 transform hover:scale-[1.02] hover:shadow-xl hover:shadow-[#4B7DFF]/30 border border-[#5081FF]/50 hover:border-[#4B7DFF]/80">
                        Close Menu
                    </button>
                </div>
            </div>
        </div>
    </div>
</body>
</html>
