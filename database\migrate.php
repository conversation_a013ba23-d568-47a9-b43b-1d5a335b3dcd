<?php
/**
 * Database Migration Runner
 * Run this script to apply database migrations for social login
 */

require_once __DIR__ . '/../config.php';

echo "=== Database Migration Runner ===\n";
echo "Adding Zalo and Discord social login columns...\n\n";

try {
    // Read the migration SQL file
    $migrationFile = __DIR__ . '/migrations/add_social_login_columns.sql';
    
    if (!file_exists($migrationFile)) {
        throw new Exception("Migration file not found: $migrationFile");
    }
    
    $sql = file_get_contents($migrationFile);
    
    // Split SQL statements by semicolon
    $statements = array_filter(array_map('trim', explode(';', $sql)));
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $statement) {
        // Skip empty statements and comments
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        echo "Executing: " . substr($statement, 0, 50) . "...\n";
        
        try {
            $result = $mysqli->query($statement);
            if ($result) {
                echo "✓ Success\n";
                $successCount++;
            } else {
                echo "✗ Error: " . $mysqli->error . "\n";
                $errorCount++;
            }
        } catch (Exception $e) {
            echo "✗ Exception: " . $e->getMessage() . "\n";
            $errorCount++;
        }
        
        echo "\n";
    }
    
    echo "=== Migration Summary ===\n";
    echo "Successful statements: $successCount\n";
    echo "Failed statements: $errorCount\n";
    
    if ($errorCount === 0) {
        echo "\n✓ All migrations completed successfully!\n";
        echo "Zalo and Discord social login columns have been added to the user table.\n";
    } else {
        echo "\n⚠ Some migrations failed. Please check the errors above.\n";
    }
    
} catch (Exception $e) {
    echo "✗ Migration failed: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n=== Next Steps ===\n";
echo "1. Update your Zalo App ID and Secret in: app/controllers/core/zalo/login.php\n";
echo "2. Update your Discord Client ID and Secret in: app/controllers/core/discord/login.php\n";
echo "3. Configure your OAuth redirect URIs in Zalo and Discord developer consoles\n";
echo "4. Test the social login functionality\n";
?>
