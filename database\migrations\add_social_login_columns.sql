-- Migration: Add Zalo and Discord ID columns to user table
-- Date: 2025-06-14
-- Purpose: Enable Zalo and Discord social login integration

-- Add zalo_id column to user table
ALTER TABLE `user` 
ADD COLUMN `zalo_id` VARCHAR(255) NULL DEFAULT NULL COMMENT 'Zalo user ID for social login' 
AFTER `google_id`;

-- Add discord_id column to user table  
ALTER TABLE `user` 
ADD COLUMN `discord_id` VARCHAR(255) NULL DEFAULT NULL COMMENT 'Discord user ID for social login' 
AFTER `zalo_id`;

-- Add indexes for better performance on social login lookups
CREATE INDEX `idx_user_zalo_id` ON `user` (`zalo_id`);
CREATE INDEX `idx_user_discord_id` ON `user` (`discord_id`);

-- Add unique constraints to prevent duplicate social accounts
ALTER TABLE `user` 
ADD CONSTRAINT `unique_zalo_id` UNIQUE (`zalo_id`),
ADD CONSTRAINT `unique_discord_id` UNIQUE (`discord_id`);
