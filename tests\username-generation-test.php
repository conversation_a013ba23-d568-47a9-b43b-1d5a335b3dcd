<?php
/**
 * Username Generation Test Script
 * Tests the Vietnamese diacritics removal and username generation logic
 * Now includes compatibility testing between custom function and to_slug()
 */

require_once __DIR__ . '/../app/controllers/core/logic.php';

// Helper function to remove Vietnamese diacritics (OLD VERSION - for comparison)
function removeVietnameseDiacritics($str) {
    $vietnamese = [
        'à', 'á', 'ạ', 'ả', 'ã', 'â', 'ầ', 'ấ', 'ậ', 'ẩ', 'ẫ', 'ă', 'ằ', 'ắ', 'ặ', 'ẳ', 'ẵ',
        'è', 'é', 'ẹ', 'ẻ', 'ẽ', 'ê', 'ề', 'ế', 'ệ', 'ể', 'ễ',
        'ì', 'í', 'ị', 'ỉ', 'ĩ',
        'ò', 'ó', 'ọ', 'ỏ', 'õ', 'ô', 'ồ', 'ố', 'ộ', 'ổ', 'ỗ', 'ơ', 'ờ', 'ớ', 'ợ', 'ở', 'ỡ',
        'ù', 'ú', 'ụ', 'ủ', 'ũ', 'ư', 'ừ', 'ứ', 'ự', 'ử', 'ữ',
        'ỳ', 'ý', 'ỵ', 'ỷ', 'ỹ',
        'đ',
        'À', 'Á', 'Ạ', 'Ả', 'Ã', 'Â', 'Ầ', 'Ấ', 'Ậ', 'Ẩ', 'Ẫ', 'Ă', 'Ằ', 'Ắ', 'Ặ', 'Ẳ', 'Ẵ',
        'È', 'É', 'Ẹ', 'Ẻ', 'Ẽ', 'Ê', 'Ề', 'Ế', 'Ệ', 'Ể', 'Ễ',
        'Ì', 'Í', 'Ị', 'Ỉ', 'Ĩ',
        'Ò', 'Ó', 'Ọ', 'Ỏ', 'Õ', 'Ô', 'Ồ', 'Ố', 'Ộ', 'Ổ', 'Ỗ', 'Ơ', 'Ờ', 'Ớ', 'Ợ', 'Ở', 'Ỡ',
        'Ù', 'Ú', 'Ụ', 'Ủ', 'Ũ', 'Ư', 'Ừ', 'Ứ', 'Ự', 'Ử', 'Ữ',
        'Ỳ', 'Ý', 'Ỵ', 'Ỷ', 'Ỹ',
        'Đ'
    ];
    
    $latin = [
        'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a',
        'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e',
        'i', 'i', 'i', 'i', 'i',
        'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o',
        'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u',
        'y', 'y', 'y', 'y', 'y',
        'd',
        'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A',
        'E', 'E', 'E', 'E', 'E', 'E', 'E', 'E', 'E', 'E', 'E',
        'I', 'I', 'I', 'I', 'I',
        'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O',
        'U', 'U', 'U', 'U', 'U', 'U', 'U', 'U', 'U', 'U', 'U',
        'Y', 'Y', 'Y', 'Y', 'Y',
        'D'
    ];
    
    return str_replace($vietnamese, $latin, $str);
}

// Function to generate username using OLD custom function (for comparison)
function generateUsernameOld($displayName, $socialId, $platform = 'social') {
    // Remove Vietnamese diacritics
    $username = removeVietnameseDiacritics($displayName);

    // Convert to lowercase and remove special characters/spaces
    $username = strtolower($username);
    $username = preg_replace('/[^a-z0-9]/', '', $username);

    // Ensure minimum length
    if (strlen($username) < 3) {
        $username = $platform . 'user' . substr($socialId, -3);
    }

    return $username;
}

// Function to generate username using NEW to_slug() function (current implementation)
function generateUsernameNew($displayName, $socialId, $platform = 'social') {
    // Use existing to_slug() function to handle Vietnamese diacritics and normalization
    $username = to_slug($displayName);

    // Remove hyphens to create clean username (to_slug keeps hyphens for URLs)
    $username = str_replace('-', '', $username);

    // Ensure minimum length
    if (strlen($username) < 3) {
        $username = $platform . 'user' . substr($socialId, -3);
    }

    return $username;
}

// Function to simulate duplicate handling
function handleDuplicateUsername($originalUsername, $socialId, $existingUsernames = []) {
    $username = $originalUsername;
    $attempt = 0;
    
    while (in_array($username, $existingUsernames)) {
        $attempt++;
        
        // Extract 1-3 random digits from social ID
        $id_digits = preg_replace('/[^0-9]/', '', $socialId);
        if (strlen($id_digits) >= 3) {
            $random_start = rand(0, strlen($id_digits) - 3);
            $suffix = substr($id_digits, $random_start, 3);
        } else {
            $suffix = str_pad($id_digits, 3, '0', STR_PAD_LEFT);
        }
        
        $username = $originalUsername . $suffix;
        
        // Fallback: if still not unique after several attempts
        if ($attempt > 10) {
            $username = $originalUsername . rand(100, 999);
            break;
        }
    }
    
    return $username;
}

echo "=== Username Generation Test Script ===\n\n";

// Test cases
$testCases = [
    // Vietnamese names
    ['Nguyễn Văn Anh', '1234567890123456', 'zalo'],
    ['Trần Thị Hương', '9876543210987654', 'discord'],
    ['Lê Minh Đức', '1111222233334444', 'zalo'],
    ['Phạm Thị Lan Anh', '5555666677778888', 'discord'],
    
    // English names
    ['John Smith', '1234567890', 'discord'],
    ['Mary Johnson', '9876543210', 'zalo'],
    
    // Mixed and special cases
    ['Café Sài Gòn', '1122334455', 'zalo'],
    ['An', '123', 'discord'], // Short name
    ['!@#$%', '456', 'zalo'], // Special characters only
    ['Nguyễn Văn A 123', '7890123456', 'discord'], // Mixed with numbers
];

echo "=== COMPATIBILITY TEST: Old vs New Username Generation ===\n";
echo str_repeat("-", 100) . "\n";
printf("%-25s %-20s %-20s %-10s\n", "Display Name", "Old Method", "New Method", "Match?");
echo str_repeat("-", 100) . "\n";

foreach ($testCases as $case) {
    $displayName = $case[0];
    $socialId = $case[1];
    $platform = $case[2];

    $usernameOld = generateUsernameOld($displayName, $socialId, $platform);
    $usernameNew = generateUsernameNew($displayName, $socialId, $platform);
    $match = ($usernameOld === $usernameNew) ? "✓ YES" : "✗ NO";

    printf("%-25s %-20s %-20s %-10s\n", $displayName, $usernameOld, $usernameNew, $match);
}

echo "\n\nTesting basic username generation (using NEW to_slug method):\n";
echo str_repeat("-", 80) . "\n";
printf("%-25s %-15s %-10s %-20s\n", "Display Name", "Social ID", "Platform", "Generated Username");
echo str_repeat("-", 80) . "\n";

foreach ($testCases as $case) {
    $displayName = $case[0];
    $socialId = $case[1];
    $platform = $case[2];

    $username = generateUsernameNew($displayName, $socialId, $platform);
    printf("%-25s %-15s %-10s %-20s\n", $displayName, $socialId, $platform, $username);
}

echo "\n\nTesting duplicate handling (using NEW method):\n";
echo str_repeat("-", 100) . "\n";
printf("%-20s %-15s %-20s %-20s %-20s\n", "Original Username", "Social ID", "Existing Usernames", "Final Username", "Suffix Used");
echo str_repeat("-", 100) . "\n";

// Test duplicate scenarios - generate usernames using new method first
$duplicateTests = [
    ['Nguyễn Văn Anh', '1234567890123456'],
    ['John Smith', '9876543210'],
    ['Trần Thị Hương', '111222333'],
];

foreach ($duplicateTests as $test) {
    $displayName = $test[0];
    $socialId = $test[1];

    // Generate base username using new method
    $originalUsername = generateUsernameNew($displayName, $socialId, 'test');

    // Simulate existing usernames
    $existingUsernames = [$originalUsername];

    $finalUsername = handleDuplicateUsername($originalUsername, $socialId, $existingUsernames);
    $suffix = str_replace($originalUsername, '', $finalUsername);

    printf("%-20s %-15s %-20s %-20s %-20s\n",
        $originalUsername,
        $socialId,
        implode(', ', $existingUsernames),
        $finalUsername,
        $suffix ?: 'None'
    );
}

echo "\n\nTesting Vietnamese diacritics removal - OLD vs NEW methods:\n";
echo str_repeat("-", 90) . "\n";
printf("%-25s %-30s %-30s %-5s\n", "Original Text", "Old Method", "New Method (to_slug)", "Match");
echo str_repeat("-", 90) . "\n";

$vietnameseTests = [
    'Nguyễn Văn Anh',
    'Trần Thị Hương',
    'Lê Minh Đức',
    'Phạm Thị Lan Anh',
    'Đặng Quốc Việt',
    'Hoàng Thị Mỹ Linh',
    'Vũ Đình Hòa',
    'Bùi Thị Thanh Tâm'
];

foreach ($vietnameseTests as $text) {
    $oldMethod = strtolower(preg_replace('/[^a-z0-9]/', '', removeVietnameseDiacritics($text)));
    $newMethod = str_replace('-', '', to_slug($text));
    $match = ($oldMethod === $newMethod) ? "✓" : "✗";

    printf("%-25s %-30s %-30s %-5s\n", $text, $oldMethod, $newMethod, $match);
}

echo "\n\nDetailed to_slug() function analysis:\n";
echo str_repeat("-", 80) . "\n";
printf("%-25s %-25s %-25s\n", "Original Text", "to_slug() Output", "Final Username");
echo str_repeat("-", 80) . "\n";

foreach ($vietnameseTests as $text) {
    $slugOutput = to_slug($text);
    $finalUsername = str_replace('-', '', $slugOutput);

    printf("%-25s %-25s %-25s\n", $text, $slugOutput, $finalUsername);
}

echo "\n=== Test completed successfully! ===\n";
?>
