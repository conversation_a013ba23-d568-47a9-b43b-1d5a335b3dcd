<?php
/**
 * Username Generation Test Script
 * Tests the Vietnamese diacritics removal and username generation logic
 */

// Helper function to remove Vietnamese diacritics (same as in social login handlers)
function removeVietnameseDiacritics($str) {
    $vietnamese = [
        'à', 'á', 'ạ', 'ả', 'ã', 'â', 'ầ', 'ấ', 'ậ', 'ẩ', 'ẫ', 'ă', 'ằ', 'ắ', 'ặ', 'ẳ', 'ẵ',
        'è', 'é', 'ẹ', 'ẻ', 'ẽ', 'ê', 'ề', 'ế', 'ệ', 'ể', 'ễ',
        'ì', 'í', 'ị', 'ỉ', 'ĩ',
        'ò', 'ó', 'ọ', 'ỏ', 'õ', 'ô', 'ồ', 'ố', 'ộ', 'ổ', 'ỗ', 'ơ', 'ờ', 'ớ', 'ợ', 'ở', 'ỡ',
        'ù', 'ú', 'ụ', 'ủ', 'ũ', 'ư', 'ừ', 'ứ', 'ự', 'ử', 'ữ',
        'ỳ', 'ý', 'ỵ', 'ỷ', 'ỹ',
        'đ',
        'À', 'Á', 'Ạ', 'Ả', 'Ã', 'Â', 'Ầ', 'Ấ', 'Ậ', 'Ẩ', 'Ẫ', 'Ă', 'Ằ', 'Ắ', 'Ặ', 'Ẳ', 'Ẵ',
        'È', 'É', 'Ẹ', 'Ẻ', 'Ẽ', 'Ê', 'Ề', 'Ế', 'Ệ', 'Ể', 'Ễ',
        'Ì', 'Í', 'Ị', 'Ỉ', 'Ĩ',
        'Ò', 'Ó', 'Ọ', 'Ỏ', 'Õ', 'Ô', 'Ồ', 'Ố', 'Ộ', 'Ổ', 'Ỗ', 'Ơ', 'Ờ', 'Ớ', 'Ợ', 'Ở', 'Ỡ',
        'Ù', 'Ú', 'Ụ', 'Ủ', 'Ũ', 'Ư', 'Ừ', 'Ứ', 'Ự', 'Ử', 'Ữ',
        'Ỳ', 'Ý', 'Ỵ', 'Ỷ', 'Ỹ',
        'Đ'
    ];
    
    $latin = [
        'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a', 'a',
        'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e', 'e',
        'i', 'i', 'i', 'i', 'i',
        'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o', 'o',
        'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u', 'u',
        'y', 'y', 'y', 'y', 'y',
        'd',
        'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A', 'A',
        'E', 'E', 'E', 'E', 'E', 'E', 'E', 'E', 'E', 'E', 'E',
        'I', 'I', 'I', 'I', 'I',
        'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O', 'O',
        'U', 'U', 'U', 'U', 'U', 'U', 'U', 'U', 'U', 'U', 'U',
        'Y', 'Y', 'Y', 'Y', 'Y',
        'D'
    ];
    
    return str_replace($vietnamese, $latin, $str);
}

// Function to generate username (same logic as in social login handlers)
function generateUsername($displayName, $socialId, $platform = 'social') {
    // Remove Vietnamese diacritics
    $username = removeVietnameseDiacritics($displayName);
    
    // Convert to lowercase and remove special characters/spaces
    $username = strtolower($username);
    $username = preg_replace('/[^a-z0-9]/', '', $username);
    
    // Ensure minimum length
    if (strlen($username) < 3) {
        $username = $platform . 'user' . substr($socialId, -3);
    }
    
    return $username;
}

// Function to simulate duplicate handling
function handleDuplicateUsername($originalUsername, $socialId, $existingUsernames = []) {
    $username = $originalUsername;
    $attempt = 0;
    
    while (in_array($username, $existingUsernames)) {
        $attempt++;
        
        // Extract 1-3 random digits from social ID
        $id_digits = preg_replace('/[^0-9]/', '', $socialId);
        if (strlen($id_digits) >= 3) {
            $random_start = rand(0, strlen($id_digits) - 3);
            $suffix = substr($id_digits, $random_start, 3);
        } else {
            $suffix = str_pad($id_digits, 3, '0', STR_PAD_LEFT);
        }
        
        $username = $originalUsername . $suffix;
        
        // Fallback: if still not unique after several attempts
        if ($attempt > 10) {
            $username = $originalUsername . rand(100, 999);
            break;
        }
    }
    
    return $username;
}

echo "=== Username Generation Test Script ===\n\n";

// Test cases
$testCases = [
    // Vietnamese names
    ['Nguyễn Văn Anh', '1234567890123456', 'zalo'],
    ['Trần Thị Hương', '9876543210987654', 'discord'],
    ['Lê Minh Đức', '1111222233334444', 'zalo'],
    ['Phạm Thị Lan Anh', '5555666677778888', 'discord'],
    
    // English names
    ['John Smith', '1234567890', 'discord'],
    ['Mary Johnson', '9876543210', 'zalo'],
    
    // Mixed and special cases
    ['Café Sài Gòn', '1122334455', 'zalo'],
    ['An', '123', 'discord'], // Short name
    ['!@#$%', '456', 'zalo'], // Special characters only
    ['Nguyễn Văn A 123', '7890123456', 'discord'], // Mixed with numbers
];

echo "Testing basic username generation:\n";
echo str_repeat("-", 80) . "\n";
printf("%-25s %-15s %-10s %-20s\n", "Display Name", "Social ID", "Platform", "Generated Username");
echo str_repeat("-", 80) . "\n";

foreach ($testCases as $case) {
    $displayName = $case[0];
    $socialId = $case[1];
    $platform = $case[2];
    
    $username = generateUsername($displayName, $socialId, $platform);
    printf("%-25s %-15s %-10s %-20s\n", $displayName, $socialId, $platform, $username);
}

echo "\n\nTesting duplicate handling:\n";
echo str_repeat("-", 100) . "\n";
printf("%-20s %-15s %-20s %-20s %-20s\n", "Original Username", "Social ID", "Existing Usernames", "Final Username", "Suffix Used");
echo str_repeat("-", 100) . "\n";

// Test duplicate scenarios
$duplicateTests = [
    ['nguyenvananh', '1234567890123456', ['nguyenvananh']],
    ['johnsmith', '9876543210', ['johnsmith', 'johnsmith876']],
    ['tranthihuong', '111222333', ['tranthihuong', 'tranthihuong111', 'tranthihuong222']],
];

foreach ($duplicateTests as $test) {
    $originalUsername = $test[0];
    $socialId = $test[1];
    $existingUsernames = $test[2];
    
    $finalUsername = handleDuplicateUsername($originalUsername, $socialId, $existingUsernames);
    $suffix = str_replace($originalUsername, '', $finalUsername);
    
    printf("%-20s %-15s %-20s %-20s %-20s\n", 
        $originalUsername, 
        $socialId, 
        implode(', ', $existingUsernames), 
        $finalUsername,
        $suffix ?: 'None'
    );
}

echo "\n\nTesting Vietnamese diacritics removal:\n";
echo str_repeat("-", 60) . "\n";
printf("%-30s %-30s\n", "Original Text", "After Diacritics Removal");
echo str_repeat("-", 60) . "\n";

$vietnameseTests = [
    'Nguyễn Văn Anh',
    'Trần Thị Hương',
    'Lê Minh Đức',
    'Phạm Thị Lan Anh',
    'Đặng Quốc Việt',
    'Hoàng Thị Mỹ Linh',
    'Vũ Đình Hòa',
    'Bùi Thị Thanh Tâm'
];

foreach ($vietnameseTests as $text) {
    $processed = removeVietnameseDiacritics($text);
    printf("%-30s %-30s\n", $text, $processed);
}

echo "\n=== Test completed successfully! ===\n";
?>
