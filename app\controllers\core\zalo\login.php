<?php
function clientZalo(){
    global $duogxaolin;
    
    // Zalo OAuth Configuration
    $app_id = '3939706819345480721'; // Replace with your actual Zalo App ID
    $app_secret = 'nOMlWKycd545sH2DIcVv'; // Replace with your actual Zalo App Secret
    $redirect_uri = home_url().'/zalo/api/login'; 
    
    // Zalo OAuth URLs
    $auth_url = 'https://oauth.zaloapp.com/v4/permission';
    $token_url = 'https://oauth.zaloapp.com/v4/access_token';
    $user_info_url = 'https://graph.zalo.me/v2.0/me';
    
    // Build authorization URL
    $params = [
        'app_id' => $app_id,
        'redirect_uri' => $redirect_uri,
        'state' => md5(uniqid(rand(), true)) // CSRF protection
    ];
    
    $authorization_url = $auth_url . '?' . http_build_query($params);
    
    return [
        'app_id' => $app_id,
        'app_secret' => $app_secret,
        'redirect_uri' => $redirect_uri,
        'auth_url' => $authorization_url,
        'token_url' => $token_url,
        'user_info_url' => $user_info_url
    ];
}

function getZaloAccessToken($code, $config) {
    $data = [
        'app_id' => $config['app_id'],
        'app_secret' => $config['app_secret'],
        'code' => $code,
        'grant_type' => 'authorization_code'
    ];
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $config['token_url']);
    curl_setopt($ch, CURLOPT_POST, true);
    curl_setopt($ch, CURLOPT_POSTFIELDS, http_build_query($data));
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/x-www-form-urlencoded'
    ]);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code === 200) {
        return json_decode($response, true);
    }
    
    return false;
}

function getZaloUserInfo($access_token, $config) {
    $url = $config['user_info_url'] . '?access_token=' . $access_token . '&fields=id,name,picture';
    
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
    curl_setopt($ch, CURLOPT_HTTPHEADER, [
        'Content-Type: application/json'
    ]);
    
    $response = curl_exec($ch);
    $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    curl_close($ch);
    
    if ($http_code === 200) {
        return json_decode($response, true);
    }
    
    return false;
}

$zalo_config = clientZalo();
$login_zalo = $zalo_config['auth_url'];
